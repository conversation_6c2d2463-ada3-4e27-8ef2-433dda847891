using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Input;
using BoardDesigner.Models;

namespace BoardDesigner.Controls
{
    public partial class ResizableComponent : UserControl
    {
        public ResizableComponent()
        {
            InitializeComponent();

            // 绑定调整手柄事件
            TopLeftThumb.DragDelta += (s, e) => {
                System.Diagnostics.Debug.WriteLine("TopLeftThumb DragDelta");
                ResizeComponent(-e.HorizontalChange, -e.Vertical<PERSON>hange, e.HorizontalChange, e.VerticalChange);
            };
            TopRightThumb.DragDelta += (s, e) => {
                System.Diagnostics.Debug.WriteLine("TopRightThumb DragDelta");
                ResizeComponent(0, -e.VerticalChange, e.HorizontalChange, e.VerticalChange);
            };
            BottomLeftThumb.DragDelta += (s, e) => {
                System.Diagnostics.Debug.WriteLine("BottomLeftThumb DragDelta");
                ResizeComponent(-e.HorizontalChange, 0, e.Horizontal<PERSON>hange, e.VerticalChange);
            };
            BottomRightThumb.DragDelta += (s, e) => {
                System.Diagnostics.Debug.WriteLine("BottomRightThumb DragDelta");
                ResizeComponent(0, 0, e.HorizontalChange, e.VerticalChange);
            };
            TopThumb.DragDelta += (s, e) => {
                System.Diagnostics.Debug.WriteLine("TopThumb DragDelta");
                ResizeComponent(0, -e.VerticalChange, 0, e.VerticalChange);
            };
            BottomThumb.DragDelta += (s, e) => {
                System.Diagnostics.Debug.WriteLine("BottomThumb DragDelta");
                ResizeComponent(0, 0, 0, e.VerticalChange);
            };
            LeftThumb.DragDelta += (s, e) => {
                System.Diagnostics.Debug.WriteLine("LeftThumb DragDelta");
                ResizeComponent(-e.HorizontalChange, 0, e.HorizontalChange, 0);
            };
            RightThumb.DragDelta += (s, e) => {
                System.Diagnostics.Debug.WriteLine("RightThumb DragDelta");
                ResizeComponent(0, 0, e.HorizontalChange, 0);
            };

            // 添加拖拽移动功能
            MainGrid.MouseLeftButtonDown += OnMouseLeftButtonDown;
            MainGrid.MouseMove += OnMouseMove;
            MainGrid.MouseLeftButtonUp += OnMouseLeftButtonUp;
        }

        private bool _isDragging;
        private Point _startPoint;

        private void OnMouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (DataContext is ComponentBase component)
            {
                // 检查是否点击在调整手柄上，如果是则不处理拖动
                if (e.OriginalSource is Thumb)
                {
                    // 让Thumb处理自己的事件，不要干扰
                    return;
                }

                _startPoint = e.GetPosition(this.Parent as UIElement);
                _isDragging = false; // 先设为false，只有移动时才开始拖拽
                MainGrid.CaptureMouse();

                // 选中组件 - 通过查找MainWindow来获取ViewModel
                var mainWindow = Application.Current.MainWindow as MainWindow;
                if (mainWindow?.DataContext is ViewModels.MainViewModel viewModel)
                {
                    viewModel.SelectedComponent = component;
                }

                e.Handled = true;
            }
        }

        private void OnMouseMove(object sender, MouseEventArgs e)
        {
            // 只有当MainGrid捕获了鼠标且没有Thumb在拖拽时才处理移动
            if (MainGrid.IsMouseCaptured && e.LeftButton == MouseButtonState.Pressed && DataContext is ComponentBase component)
            {
                // 检查是否有Thumb正在被拖拽
                if (e.OriginalSource is Thumb)
                    return;

                var currentPoint = e.GetPosition(this.Parent as UIElement);

                if (!_isDragging)
                {
                    // 检查是否超过拖拽阈值
                    var distance = Math.Abs(currentPoint.X - _startPoint.X) + Math.Abs(currentPoint.Y - _startPoint.Y);
                    if (distance > 5) // 5像素的拖拽阈值
                    {
                        _isDragging = true;
                    }
                }

                if (_isDragging)
                {
                    var deltaX = currentPoint.X - _startPoint.X;
                    var deltaY = currentPoint.Y - _startPoint.Y;

                    component.X = Math.Max(0, component.X + deltaX);
                    component.Y = Math.Max(0, component.Y + deltaY);

                    _startPoint = currentPoint;
                }
            }
        }

        private void OnMouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            if (MainGrid.IsMouseCaptured)
            {
                _isDragging = false;
                MainGrid.ReleaseMouseCapture();
            }
        }

        private void ResizeComponent(double deltaX, double deltaY, double deltaWidth, double deltaHeight)
        {
            if (DataContext is ComponentBase component)
            {
                // 最小尺寸限制
                const double minSize = 20;

                // 计算新的位置和尺寸
                var newX = component.X + deltaX;
                var newY = component.Y + deltaY;
                var newWidth = Math.Max(minSize, component.Width + deltaWidth);
                var newHeight = Math.Max(minSize, component.Height + deltaHeight);

                // 确保不会移动到负坐标
                if (newX >= 0 && newY >= 0)
                {
                    component.X = newX;
                    component.Y = newY;
                }

                component.Width = newWidth;
                component.Height = newHeight;
            }
        }
    }
}
