using System;
using System.Windows.Media;
using System.Collections.ObjectModel;

namespace BoardDesigner.Models
{
    public enum DeviceStatus
    {
        Normal,    // 正常 - 绿色
        Warning,   // 警告 - 黄色
        Error,     // 错误 - 红色
        Offline    // 离线 - 灰色
    }

    public class DeviceBoard : ComponentBase
    {
        private DeviceStatus _status = DeviceStatus.Normal;
        private string _deviceName = "设备";
        private string _deviceType = "传感器";
        private string _batchNumber = "批次001";
        private double _temperature = 25.0;
        private double _humidity = 60.0;
        private string _location = "车间A";
        private ObservableCollection<CustomProperty> _customProperties = new();
        private ObservableCollection<CustomProperty> _dynamicProperties = new();

        // 原有属性的可见性控制
        private bool _deviceNameVisible = true;
        private bool _deviceTypeVisible = true;
        private bool _locationVisible = true;
        private bool _batchNumberVisible = true;
        private bool _temperatureVisible = true;
        private bool _humidityVisible = true;

        public override string ComponentType => "DeviceBoard";
        public override string DisplayName => "设备";

        public DeviceStatus Status
        {
            get => _status;
            set
            {
                if (SetProperty(ref _status, value))
                {
                    OnPropertyChanged(nameof(StatusColor));
                }
            }
        }

        public string DeviceName
        {
            get => _deviceName;
            set => SetProperty(ref _deviceName, value);
        }

        public string DeviceType
        {
            get => _deviceType;
            set => SetProperty(ref _deviceType, value);
        }

        public string BatchNumber
        {
            get => _batchNumber;
            set => SetProperty(ref _batchNumber, value);
        }

        public double Temperature
        {
            get => _temperature;
            set => SetProperty(ref _temperature, value);
        }

        public double Humidity
        {
            get => _humidity;
            set => SetProperty(ref _humidity, value);
        }

        public string Location
        {
            get => _location;
            set => SetProperty(ref _location, value);
        }

        public ObservableCollection<CustomProperty> CustomProperties
        {
            get => _customProperties;
            set => SetProperty(ref _customProperties, value);
        }

        public ObservableCollection<CustomProperty> DynamicProperties
        {
            get => _dynamicProperties;
            set => SetProperty(ref _dynamicProperties, value);
        }

        // 原有属性的可见性控制属性
        public bool DeviceNameVisible
        {
            get => _deviceNameVisible;
            set => SetProperty(ref _deviceNameVisible, value);
        }

        public bool DeviceTypeVisible
        {
            get => _deviceTypeVisible;
            set => SetProperty(ref _deviceTypeVisible, value);
        }

        public bool LocationVisible
        {
            get => _locationVisible;
            set => SetProperty(ref _locationVisible, value);
        }

        public bool BatchNumberVisible
        {
            get => _batchNumberVisible;
            set => SetProperty(ref _batchNumberVisible, value);
        }

        public bool TemperatureVisible
        {
            get => _temperatureVisible;
            set => SetProperty(ref _temperatureVisible, value);
        }

        public bool HumidityVisible
        {
            get => _humidityVisible;
            set => SetProperty(ref _humidityVisible, value);
        }

        public Brush StatusColor
        {
            get
            {
                return Status switch
                {
                    DeviceStatus.Normal => Brushes.Green,
                    DeviceStatus.Warning => Brushes.Orange,
                    DeviceStatus.Error => Brushes.Red,
                    DeviceStatus.Offline => Brushes.Gray,
                    _ => Brushes.Gray
                };
            }
        }

        public DeviceBoard()
        {
            Width = 150;
            Height = 120;
            Name = "设备";
        }
    }
}
