using System;
using System.Globalization;
using System.Windows.Data;

namespace BoardDesigner.Converters
{
    public class ThumbnailScaleConverter : IValueConverter
    {
        // 缩略图缩放比例：主画布2000x1500 -> 缩略图200x150，比例为1:10
        private const double SCALE_FACTOR = 0.1;

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is double doubleValue)
            {
                return doubleValue * SCALE_FACTOR;
            }
            return 0.0;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is double doubleValue)
            {
                return doubleValue / SCALE_FACTOR;
            }
            return 0.0;
        }
    }
}
