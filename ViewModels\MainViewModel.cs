using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using BoardDesigner.Models;
using BoardDesigner.Services;

namespace BoardDesigner.ViewModels
{
    public class MainViewModel : INotifyPropertyChanged
    {
        private ComponentBase? _selectedComponent;

        public ObservableCollection<ComponentBase> CanvasComponents { get; } = new();
        public ObservableCollection<ComponentBase> ToolboxComponents { get; } = new();

        public ComponentBase? SelectedComponent
        {
            get => _selectedComponent;
            set
            {
                if (_selectedComponent != null)
                    _selectedComponent.IsSelected = false;

                SetProperty(ref _selectedComponent, value);

                if (_selectedComponent != null)
                    _selectedComponent.IsSelected = true;
            }
        }

        public ICommand AddComponentCommand { get; }
        public ICommand DeleteComponentCommand { get; }
        public ICommand ClearCanvasCommand { get; }
        public ICommand NewProjectCommand { get; }
        public ICommand OpenProjectCommand { get; }
        public ICommand SaveProjectCommand { get; }
        public ICommand SaveProjectAsCommand { get; }
        public ICommand AlignToGridCommand { get; }
        public ICommand ToggleGridCommand { get; }
        public ICommand CopyComponentCommand { get; }
        public ICommand PasteComponentCommand { get; }
        public ICommand DeselectComponentCommand { get; }
        public ICommand ZoomInCommand { get; }
        public ICommand ZoomOutCommand { get; }
        public ICommand ResetZoomCommand { get; }

        private bool _showGrid = true;
        private ComponentBase? _copiedComponent;
        private double _zoomFactor = 1.0;
        private double _canvasTranslateX = 0.0;
        private double _canvasTranslateY = 0.0;
        private string? _currentProjectPath;
        private bool _isProjectModified = false;
        private string _projectName = "未命名项目";

        // 视口指示器属性 (缩略图尺寸: 200x150, 画布尺寸: 2000x1500)
        private double _viewportIndicatorLeft = 0;
        private double _viewportIndicatorTop = 0;
        private double _viewportIndicatorWidth = 50;  // 默认视口宽度
        private double _viewportIndicatorHeight = 37.5; // 默认视口高度

        // 实际视口大小
        private double _actualViewportWidth = 800.0;
        private double _actualViewportHeight = 600.0;

        private readonly ProjectService _projectService;

        public bool ShowGrid
        {
            get => _showGrid;
            set => SetProperty(ref _showGrid, value);
        }

        public double ZoomFactor
        {
            get => _zoomFactor;
            set
            {
                SetProperty(ref _zoomFactor, Math.Max(0.1, Math.Min(5.0, value)));
                UpdateViewportIndicator();
            }
        }

        public double CanvasTranslateX
        {
            get => _canvasTranslateX;
            set
            {
                SetProperty(ref _canvasTranslateX, value);
                UpdateViewportIndicator();
            }
        }

        public double CanvasTranslateY
        {
            get => _canvasTranslateY;
            set
            {
                SetProperty(ref _canvasTranslateY, value);
                UpdateViewportIndicator();
            }
        }

        public string ProjectName
        {
            get => _projectName;
            set => SetProperty(ref _projectName, value);
        }

        public bool IsProjectModified
        {
            get => _isProjectModified;
            set => SetProperty(ref _isProjectModified, value);
        }

        public string CurrentProjectPath
        {
            get => _currentProjectPath ?? "";
            set => SetProperty(ref _currentProjectPath, value);
        }

        // 视口指示器属性
        public double ViewportIndicatorLeft
        {
            get => _viewportIndicatorLeft;
            set => SetProperty(ref _viewportIndicatorLeft, value);
        }

        public double ViewportIndicatorTop
        {
            get => _viewportIndicatorTop;
            set => SetProperty(ref _viewportIndicatorTop, value);
        }

        public double ViewportIndicatorWidth
        {
            get => _viewportIndicatorWidth;
            set => SetProperty(ref _viewportIndicatorWidth, value);
        }

        public double ViewportIndicatorHeight
        {
            get => _viewportIndicatorHeight;
            set => SetProperty(ref _viewportIndicatorHeight, value);
        }

        public MainViewModel()
        {
            _projectService = new ProjectService();
            InitializeToolbox();

            // 监听组件变化以标记项目为已修改
            CanvasComponents.CollectionChanged += (s, e) => MarkProjectAsModified();

            AddComponentCommand = new RelayCommand<ComponentBase>(AddComponent);
            DeleteComponentCommand = new RelayCommand(DeleteSelectedComponent, () => SelectedComponent != null);
            ClearCanvasCommand = new RelayCommand(ClearCanvas);
            NewProjectCommand = new RelayCommand(NewProject);
            OpenProjectCommand = new RelayCommand(OpenProject);
            SaveProjectCommand = new RelayCommand(SaveProject);
            SaveProjectAsCommand = new RelayCommand(SaveProjectAs);
            AlignToGridCommand = new RelayCommand(AlignToGrid, () => SelectedComponent != null);
            ToggleGridCommand = new RelayCommand(ToggleGrid);
            CopyComponentCommand = new RelayCommand(CopyComponent, () => SelectedComponent != null);
            PasteComponentCommand = new RelayCommand(PasteComponent, () => _copiedComponent != null);
            DeselectComponentCommand = new RelayCommand(DeselectComponent);
            ZoomInCommand = new RelayCommand(ZoomIn);
            ZoomOutCommand = new RelayCommand(ZoomOut);
            ResetZoomCommand = new RelayCommand(ResetZoom);

            // 初始化视口指示器
            UpdateViewportIndicator();
        }

        private void InitializeToolbox()
        {
            ToolboxComponents.Add(new DeviceBoard());
            ToolboxComponents.Add(new Wall());
            ToolboxComponents.Add(new Area());
            ToolboxComponents.Add(new AlarmLight());
        }

        private void AddComponent(ComponentBase? template)
        {
            if (template == null) return;

            ComponentBase newComponent = template.ComponentType switch
            {
                "DeviceBoard" => new DeviceBoard(),
                "Wall" => new Wall(),
                "Area" => new Area(),
                "AlarmLight" => new AlarmLight(),
                _ => throw new ArgumentException("Unknown component type")
            };

            // 设置初始位置（避免重叠）
            newComponent.X = 50 + (CanvasComponents.Count * 20);
            newComponent.Y = 50 + (CanvasComponents.Count * 20);

            CanvasComponents.Add(newComponent);
            SelectedComponent = newComponent;
        }

        private void DeleteSelectedComponent()
        {
            if (SelectedComponent != null)
            {
                CanvasComponents.Remove(SelectedComponent);
                SelectedComponent = null;
            }
        }

        private void ClearCanvas()
        {
            CanvasComponents.Clear();
            SelectedComponent = null;
        }

        private void NewProject()
        {
            if (IsProjectModified)
            {
                var result = MessageBox.Show(
                    "当前项目有未保存的更改，是否保存？",
                    "新建项目",
                    MessageBoxButton.YesNoCancel,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    SaveProject();
                    if (IsProjectModified) return; // 如果保存失败，不继续
                }
                else if (result == MessageBoxResult.Cancel)
                {
                    return;
                }
            }

            ClearCanvas();
            _currentProjectPath = null;
            ProjectName = "未命名项目";
            IsProjectModified = false;
        }

        private async void OpenProject()
        {
            if (IsProjectModified)
            {
                var result = MessageBox.Show(
                    "当前项目有未保存的更改，是否保存？",
                    "打开项目",
                    MessageBoxButton.YesNoCancel,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    SaveProject();
                    if (IsProjectModified) return; // 如果保存失败，不继续
                }
                else if (result == MessageBoxResult.Cancel)
                {
                    return;
                }
            }

            var openFileDialog = new Microsoft.Win32.OpenFileDialog
            {
                Filter = "Board Designer Files (*.bd)|*.bd|JSON Files (*.json)|*.json|All Files (*.*)|*.*",
                Title = "打开项目文件",
                DefaultExt = "bd"
            };

            if (openFileDialog.ShowDialog() == true)
            {
                try
                {
                    await LoadProjectFromFile(openFileDialog.FileName);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"打开文件失败: {ex.Message}", "错误",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private async void SaveProject()
        {
            if (!string.IsNullOrEmpty(_currentProjectPath))
            {
                // 如果已有文件路径，直接保存
                await SaveProjectToFile(_currentProjectPath);
            }
            else
            {
                // 否则显示另存为对话框
                SaveProjectAs();
            }
        }

        private async void SaveProjectAs()
        {
            var saveFileDialog = new Microsoft.Win32.SaveFileDialog
            {
                Filter = "Board Designer Files (*.bd)|*.bd|JSON Files (*.json)|*.json|All Files (*.*)|*.*",
                Title = "保存项目文件",
                DefaultExt = "bd",
                FileName = ProjectName
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                await SaveProjectToFile(saveFileDialog.FileName);
            }
        }

        private void AlignToGrid()
        {
            if (SelectedComponent == null) return;

            const int gridSize = 20;
            SelectedComponent.X = Math.Round(SelectedComponent.X / gridSize) * gridSize;
            SelectedComponent.Y = Math.Round(SelectedComponent.Y / gridSize) * gridSize;
        }

        private void ToggleGrid()
        {
            ShowGrid = !ShowGrid;
        }

        private void DeselectComponent()
        {
            SelectedComponent = null;
        }

        private void ZoomIn()
        {
            ZoomFactor *= 1.2;
        }

        private void ZoomOut()
        {
            ZoomFactor /= 1.2;
        }

        private void ResetZoom()
        {
            ZoomFactor = 1.0;
            CanvasTranslateX = 0.0;
            CanvasTranslateY = 0.0;
        }

        public void ZoomAtPoint(double delta, double mouseX, double mouseY)
        {
            var oldZoom = ZoomFactor;
            var zoomChange = delta > 0 ? 1.1 : 1.0 / 1.1;
            var newZoom = Math.Max(0.1, Math.Min(5.0, oldZoom * zoomChange));

            if (Math.Abs(newZoom - oldZoom) > 0.001)
            {
                // 计算缩放中心点
                var zoomRatio = newZoom / oldZoom;
                CanvasTranslateX = mouseX - (mouseX - CanvasTranslateX) * zoomRatio;
                CanvasTranslateY = mouseY - (mouseY - CanvasTranslateY) * zoomRatio;
                ZoomFactor = newZoom;
            }
        }

        private void CopyComponent()
        {
            if (SelectedComponent == null) return;

            _copiedComponent = SelectedComponent.ComponentType switch
            {
                "DeviceBoard" => CloneDeviceBoard((DeviceBoard)SelectedComponent),
                "Wall" => CloneWall((Wall)SelectedComponent),
                "Area" => CloneArea((Area)SelectedComponent),
                "AlarmLight" => CloneAlarmLight((AlarmLight)SelectedComponent),
                _ => null
            };
        }

        private void PasteComponent()
        {
            if (_copiedComponent == null) return;

            ComponentBase? newComponent = _copiedComponent.ComponentType switch
            {
                "DeviceBoard" => CloneDeviceBoard((DeviceBoard)_copiedComponent),
                "Wall" => CloneWall((Wall)_copiedComponent),
                "Area" => CloneArea((Area)_copiedComponent),
                "AlarmLight" => CloneAlarmLight((AlarmLight)_copiedComponent),
                _ => null
            };

            if (newComponent != null)
            {
                newComponent.X += 20; // 偏移一点位置
                newComponent.Y += 20;
                CanvasComponents.Add(newComponent);
                SelectedComponent = newComponent;
            }
        }

        // 克隆方法
        private DeviceBoard CloneDeviceBoard(DeviceBoard original)
        {
            return new DeviceBoard
            {
                X = original.X,
                Y = original.Y,
                Width = original.Width,
                Height = original.Height,
                Name = original.Name + "_副本",
                DeviceName = original.DeviceName,
                DeviceType = original.DeviceType,
                Location = original.Location,
                BatchNumber = original.BatchNumber,
                Temperature = original.Temperature,
                Humidity = original.Humidity,
                Status = original.Status
            };
        }

        private Wall CloneWall(Wall original)
        {
            return new Wall
            {
                X = original.X,
                Y = original.Y,
                Width = original.Width,
                Height = original.Height,
                Name = original.Name + "_副本",
                FillColor = original.FillColor,
                BorderColor = original.BorderColor,
                BorderThickness = original.BorderThickness
            };
        }

        private Area CloneArea(Area original)
        {
            return new Area
            {
                X = original.X,
                Y = original.Y,
                Width = original.Width,
                Height = original.Height,
                Name = original.Name + "_副本",
                AreaName = original.AreaName,
                Description = original.Description,
                FillColor = original.FillColor,
                BorderColor = original.BorderColor,
                BorderThickness = original.BorderThickness
            };
        }

        private AlarmLight CloneAlarmLight(AlarmLight original)
        {
            return new AlarmLight
            {
                X = original.X,
                Y = original.Y,
                Width = original.Width,
                Height = original.Height,
                Name = original.Name + "_副本",
                AlarmStatus = original.AlarmStatus,
                AlarmMessage = original.AlarmMessage,
                IsBlinking = original.IsBlinking
            };
        }

        private async Task SaveProjectToFile(string filePath)
        {
            try
            {
                var projectData = new ProjectData
                {
                    ProjectName = ProjectName,
                    Canvas = new CanvasSettings
                    {
                        Width = 2000,
                        Height = 1500,
                        ShowGrid = ShowGrid,
                        ZoomFactor = ZoomFactor,
                        TranslateX = CanvasTranslateX,
                        TranslateY = CanvasTranslateY
                    },
                    Components = CanvasComponents.Select(c => _projectService.ConvertToComponentData(c)).ToList()
                };

                await _projectService.SaveProjectAsync(filePath, projectData);

                _currentProjectPath = filePath;
                ProjectName = Path.GetFileNameWithoutExtension(filePath);
                IsProjectModified = false;

                MessageBox.Show("项目保存成功！", "保存完成",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存项目失败: {ex.Message}", "错误",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadProjectFromFile(string filePath)
        {
            try
            {
                var projectData = await _projectService.LoadProjectAsync(filePath);

                // 清空当前画布
                CanvasComponents.Clear();
                SelectedComponent = null;

                // 恢复画布设置
                ShowGrid = projectData.Canvas.ShowGrid;
                ZoomFactor = projectData.Canvas.ZoomFactor;
                CanvasTranslateX = projectData.Canvas.TranslateX;
                CanvasTranslateY = projectData.Canvas.TranslateY;

                // 加载组件
                foreach (var componentData in projectData.Components)
                {
                    var component = _projectService.ConvertToComponent(componentData);
                    CanvasComponents.Add(component);
                }

                _currentProjectPath = filePath;
                ProjectName = projectData.ProjectName;
                IsProjectModified = false;

                MessageBox.Show($"项目加载成功！\n加载了 {projectData.Components.Count} 个组件", "加载完成",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载项目失败: {ex.Message}", "错误",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void MarkProjectAsModified()
        {
            IsProjectModified = true;
        }

        /// <summary>
        /// 更新视口大小
        /// </summary>
        public void UpdateViewportSize(double width, double height)
        {
            _actualViewportWidth = width;
            _actualViewportHeight = height;
            UpdateViewportIndicator();
        }

        /// <summary>
        /// 更新视口指示器的位置和大小
        /// </summary>
        private void UpdateViewportIndicator()
        {
            // 缩略图尺寸: 200x150, 画布尺寸: 2000x1500
            const double thumbnailWidth = 200.0;
            const double thumbnailHeight = 150.0;
            const double canvasWidth = 2000.0;
            const double canvasHeight = 1500.0;

            // 计算缩放比例
            var scaleX = thumbnailWidth / canvasWidth;
            var scaleY = thumbnailHeight / canvasHeight;

            // 计算视口在缩略图中的位置 (考虑平移)
            ViewportIndicatorLeft = Math.Max(0, Math.Min(thumbnailWidth, -CanvasTranslateX * scaleX));
            ViewportIndicatorTop = Math.Max(0, Math.Min(thumbnailHeight, -CanvasTranslateY * scaleY));

            // 计算视口在缩略图中的大小 (考虑缩放)
            ViewportIndicatorWidth = Math.Min(thumbnailWidth - ViewportIndicatorLeft,
                (_actualViewportWidth / ZoomFactor) * scaleX);
            ViewportIndicatorHeight = Math.Min(thumbnailHeight - ViewportIndicatorTop,
                (_actualViewportHeight / ZoomFactor) * scaleY);
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);

            // 标记项目为已修改（除了一些特殊属性）
            if (propertyName != nameof(IsProjectModified) &&
                propertyName != nameof(ProjectName) &&
                propertyName != nameof(CurrentProjectPath))
            {
                MarkProjectAsModified();
            }

            return true;
        }
    }

    public class RelayCommand : ICommand
    {
        private readonly Action _execute;
        private readonly Func<bool>? _canExecute;

        public RelayCommand(Action execute, Func<bool>? canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }

        public event EventHandler? CanExecuteChanged
        {
            add { CommandManager.RequerySuggested += value; }
            remove { CommandManager.RequerySuggested -= value; }
        }

        public bool CanExecute(object? parameter) => _canExecute?.Invoke() ?? true;
        public void Execute(object? parameter) => _execute();
    }

    public class RelayCommand<T> : ICommand
    {
        private readonly Action<T?> _execute;
        private readonly Func<T?, bool>? _canExecute;

        public RelayCommand(Action<T?> execute, Func<T?, bool>? canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }

        public event EventHandler? CanExecuteChanged
        {
            add { CommandManager.RequerySuggested += value; }
            remove { CommandManager.RequerySuggested -= value; }
        }

        public bool CanExecute(object? parameter) => _canExecute?.Invoke((T?)parameter) ?? true;
        public void Execute(object? parameter) => _execute((T?)parameter);
    }
}
