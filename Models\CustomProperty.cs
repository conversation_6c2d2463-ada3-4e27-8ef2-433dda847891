using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace BoardDesigner.Models
{
    /// <summary>
    /// 自定义属性类
    /// </summary>
    public class CustomProperty : INotifyPropertyChanged
    {
        private string _name = "";
        private string _value = "";
        private string _type = "文本";
        private bool _isVisible = true;

        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        public string Value
        {
            get => _value;
            set => SetProperty(ref _value, value);
        }

        public string Type
        {
            get => _type;
            set => SetProperty(ref _type, value);
        }

        public bool IsVisible
        {
            get => _isVisible;
            set => SetProperty(ref _isVisible, value);
        }

        public CustomProperty()
        {
        }

        public CustomProperty(string name, string value, string type = "文本", bool isVisible = true)
        {
            Name = name;
            Value = value;
            Type = type;
            IsVisible = isVisible;
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }
    }
}
