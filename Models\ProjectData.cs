using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace BoardDesigner.Models
{
    /// <summary>
    /// 项目数据模型，用于序列化保存
    /// </summary>
    public class ProjectData
    {
        public string ProjectName { get; set; } = "未命名项目";
        public string Version { get; set; } = "1.0";
        public DateTime CreatedTime { get; set; } = DateTime.Now;
        public DateTime LastModifiedTime { get; set; } = DateTime.Now;
        public string Description { get; set; } = "";

        // 画布设置
        public CanvasSettings Canvas { get; set; } = new();

        // 组件列表 - 使用object类型支持多态序列化
        public List<object> Components { get; set; } = new();
    }

    /// <summary>
    /// 画布设置
    /// </summary>
    public class CanvasSettings
    {
        public double Width { get; set; } = 2000;
        public double Height { get; set; } = 1500;
        public bool ShowGrid { get; set; } = true;
        public double ZoomFactor { get; set; } = 1.0;
        public double TranslateX { get; set; } = 0.0;
        public double TranslateY { get; set; } = 0.0;
    }

    /// <summary>
    /// 组件数据基类
    /// </summary>
    public class ComponentData
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string ComponentType { get; set; } = "";
        public string Name { get; set; } = "";
        public double X { get; set; }
        public double Y { get; set; }
        public double Width { get; set; }
        public double Height { get; set; }

        // 使用字典存储特定组件的属性
        public Dictionary<string, object> Properties { get; set; } = new();
    }

    /// <summary>
    /// 设备看板数据
    /// </summary>
    public class DeviceBoardData : ComponentData
    {
        public DeviceBoardData()
        {
            ComponentType = "DeviceBoard";
        }

        public string DeviceName { get; set; } = "";
        public string DeviceType { get; set; } = "";
        public string Location { get; set; } = "";
        public string BatchNumber { get; set; } = "";
        public double Temperature { get; set; }
        public double Humidity { get; set; }
        public DeviceStatus Status { get; set; } = DeviceStatus.Normal;
        public List<CustomPropertyData> CustomProperties { get; set; } = new();
    }

    /// <summary>
    /// 墙体数据
    /// </summary>
    public class WallData : ComponentData
    {
        public WallData()
        {
            ComponentType = "Wall";
        }

        public string FillColor { get; set; } = "#808080";
        public string BorderColor { get; set; } = "#000000";
        public double BorderThickness { get; set; } = 2.0;
    }

    /// <summary>
    /// 区域数据
    /// </summary>
    public class AreaData : ComponentData
    {
        public AreaData()
        {
            ComponentType = "Area";
        }

        public string AreaName { get; set; } = "";
        public string Description { get; set; } = "";
        public string FillColor { get; set; } = "#ADD8E6";
        public string BorderColor { get; set; } = "#0000FF";
        public double BorderThickness { get; set; } = 1.0;
    }

    /// <summary>
    /// 报警灯数据
    /// </summary>
    public class AlarmLightData : ComponentData
    {
        public AlarmLightData()
        {
            ComponentType = "AlarmLight";
        }

        public AlarmStatus AlarmStatus { get; set; } = AlarmStatus.Off;
        public string AlarmMessage { get; set; } = "";
        public bool IsBlinking { get; set; } = false;
    }
}
