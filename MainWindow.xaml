<Window x:Class="BoardDesigner.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
        xmlns:behaviors="clr-namespace:BoardDesigner.Behaviors"
        xmlns:viewmodels="clr-namespace:BoardDesigner.ViewModels"
        xmlns:models="clr-namespace:BoardDesigner.Models"
        xmlns:controls="clr-namespace:BoardDesigner.Controls"
        xmlns:converters="clr-namespace:BoardDesigner.Converters"
        Title="设备看板设计器" Height="800" Width="1200"
        WindowState="Maximized">

    <Window.Resources>
        <converters:ThumbnailScaleConverter x:Key="ThumbnailScaleConverter"/>
        <converters:BoolToGridStatusConverter x:Key="BoolToGridStatusConverter"/>
    </Window.Resources>

    <Window.DataContext>
        <viewmodels:MainViewModel/>
    </Window.DataContext>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 工具栏 -->
        <ToolBar Grid.Row="0" Background="#F8F9FA" BorderBrush="#DEE2E6" BorderThickness="0,0,0,1">
            <!-- 文件操作 -->
            <Button Command="{Binding NewProjectCommand}" Padding="8,4" ToolTip="新建项目 (Ctrl+N)">
                <StackPanel Orientation="Horizontal">
                    <Path Data="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"
                          Fill="#495057" Width="16" Height="16" Stretch="Uniform" VerticalAlignment="Center"/>
                    <TextBlock Text="新建" Margin="4,0,0,0" VerticalAlignment="Center"/>
                </StackPanel>
            </Button>
            <Button Command="{Binding OpenProjectCommand}" Padding="8,4" ToolTip="打开项目 (Ctrl+O)">
                <StackPanel Orientation="Horizontal">
                    <Path Data="M10,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V8C22,6.89 21.1,6 20,6H12L10,4Z"
                          Fill="#495057" Width="16" Height="16" Stretch="Uniform" VerticalAlignment="Center"/>
                    <TextBlock Text="打开" Margin="4,0,0,0" VerticalAlignment="Center"/>
                </StackPanel>
            </Button>
            <Button Command="{Binding SaveProjectCommand}" Padding="8,4" ToolTip="保存项目 (Ctrl+S)">
                <StackPanel Orientation="Horizontal">
                    <Path Data="M15,9H5V5H15M12,19A3,3 0 0,1 9,16A3,3 0 0,1 12,13A3,3 0 0,1 15,16A3,3 0 0,1 12,19M17,3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V7L17,3Z"
                          Fill="#495057" Width="16" Height="16" Stretch="Uniform" VerticalAlignment="Center"/>
                    <TextBlock Text="保存" Margin="4,0,0,0" VerticalAlignment="Center"/>
                </StackPanel>
            </Button>
            <Button Command="{Binding SaveProjectAsCommand}" Padding="8,4" ToolTip="另存为 (Ctrl+Shift+S)">
                <StackPanel Orientation="Horizontal">
                    <Path Data="M17,3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V7L17,3M17,7V19H5V5H15V7H17M8,12V16H16V12H8M10,10H14V14H10V10Z"
                          Fill="#495057" Width="16" Height="16" Stretch="Uniform" VerticalAlignment="Center"/>
                    <TextBlock Text="另存为" Margin="4,0,0,0" VerticalAlignment="Center"/>
                </StackPanel>
            </Button>

            <Separator/>

            <!-- 编辑操作 -->
            <Button Command="{Binding CopyComponentCommand}" Padding="8,4" ToolTip="复制组件 (Ctrl+C)">
                <StackPanel Orientation="Horizontal">
                    <Path Data="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"
                          Fill="#495057" Width="16" Height="16" Stretch="Uniform" VerticalAlignment="Center"/>
                    <TextBlock Text="复制" Margin="4,0,0,0" VerticalAlignment="Center"/>
                </StackPanel>
            </Button>
            <Button Command="{Binding PasteComponentCommand}" Padding="8,4" ToolTip="粘贴组件 (Ctrl+V)">
                <StackPanel Orientation="Horizontal">
                    <Path Data="M19,20H5V4H7V7H17V4H19M12,2A1,1 0 0,1 13,3A1,1 0 0,1 12,4A1,1 0 0,1 11,3A1,1 0 0,1 12,2M19,2H14.82C14.4,0.84 13.3,0 12,0C10.7,0 9.6,0.84 9.18,2H5A2,2 0 0,0 3,4V20A2,2 0 0,0 5,22H19A2,2 0 0,0 21,20V4A2,2 0 0,0 19,2Z"
                          Fill="#495057" Width="16" Height="16" Stretch="Uniform" VerticalAlignment="Center"/>
                    <TextBlock Text="粘贴" Margin="4,0,0,0" VerticalAlignment="Center"/>
                </StackPanel>
            </Button>
            <Button Command="{Binding DeleteComponentCommand}" Padding="8,4" ToolTip="删除选中组件 (Delete)">
                <StackPanel Orientation="Horizontal">
                    <Path Data="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z"
                          Fill="#DC3545" Width="16" Height="16" Stretch="Uniform" VerticalAlignment="Center"/>
                    <TextBlock Text="删除" Margin="4,0,0,0" VerticalAlignment="Center"/>
                </StackPanel>
            </Button>
            <Button Command="{Binding ClearCanvasCommand}" Padding="8,4" ToolTip="清空画布">
                <StackPanel Orientation="Horizontal">
                    <Path Data="M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M19,5V19H5V5H19Z"
                          Fill="#FFC107" Width="16" Height="16" Stretch="Uniform" VerticalAlignment="Center"/>
                    <TextBlock Text="清空" Margin="4,0,0,0" VerticalAlignment="Center"/>
                </StackPanel>
            </Button>

            <Separator/>

            <!-- 视图操作 -->
            <Button Command="{Binding AlignToGridCommand}" Padding="8,4" ToolTip="对齐到网格">
                <StackPanel Orientation="Horizontal">
                    <Path Data="M10,4V8H14V4H16V8H20V10H16V14H20V16H16V20H14V16H10V20H8V16H4V14H8V10H4V8H8V4H10M14,14V10H10V14H14Z"
                          Fill="#495057" Width="16" Height="16" Stretch="Uniform" VerticalAlignment="Center"/>
                    <TextBlock Text="网格对齐" Margin="4,0,0,0" VerticalAlignment="Center"/>
                </StackPanel>
            </Button>
            <ToggleButton IsChecked="{Binding ShowGrid}" Padding="8,4" ToolTip="显示/隐藏网格">
                <StackPanel Orientation="Horizontal">
                    <Path Data="M8,4V8H4V4H8M10,4H14V8H10V4M16,4H20V8H16V4M8,10V14H4V10H8M10,10H14V14H10V10M16,10H20V14H16V10M8,16V20H4V16H8M10,16H14V20H10V16M16,16H20V20H16V16Z"
                          Fill="#495057" Width="16" Height="16" Stretch="Uniform" VerticalAlignment="Center"/>
                    <TextBlock Text="显示网格" Margin="4,0,0,0" VerticalAlignment="Center"/>
                </StackPanel>
            </ToggleButton>
        </ToolBar>

        <!-- 主要内容区域 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="200"/>
                <ColumnDefinition Width="5"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="5"/>
                <ColumnDefinition Width="250"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧工具箱 -->
            <DockPanel Grid.Column="0" Background="#F8F9FA">
                <!-- 工具箱标题 -->
                <Border DockPanel.Dock="Top" Background="#E9ECEF" BorderBrush="#DEE2E6" BorderThickness="0,0,0,1" Padding="12,8">
                    <StackPanel Orientation="Horizontal">
                        <Path Data="M3,3H21V5H3V3M3,7H21V9H3V7M3,11H21V13H3V11M3,15H21V17H3V15M3,19H21V21H3V19Z"
                              Fill="#6C757D" Width="16" Height="16" Stretch="Uniform" VerticalAlignment="Center"/>
                        <TextBlock Text="工具箱" FontWeight="SemiBold" FontSize="14" Foreground="#495057" Margin="8,0,0,0" VerticalAlignment="Center"/>
                    </StackPanel>
                </Border>

                <!-- 工具箱内容 -->
                <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled" Padding="8">
                    <StackPanel>
                        <!-- 设备组件分组 -->
                        <Expander Header="设备组件" IsExpanded="True" Margin="0,0,0,8">
                            <Expander.HeaderTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <Path Data="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8Z"
                                              Fill="#28A745" Width="14" Height="14" Stretch="Uniform" VerticalAlignment="Center"/>
                                        <TextBlock Text="{Binding}" FontWeight="Medium" FontSize="12" Foreground="#495057" Margin="6,0,0,0"/>
                                    </StackPanel>
                                </DataTemplate>
                            </Expander.HeaderTemplate>
                            <StackPanel Margin="0,8,0,0">
                                <!-- 设备看板 -->
                                <Button Style="{StaticResource ToolboxButtonStyle}" Tag="{Binding ToolboxComponents[0]}" Click="ToolboxButton_Click">
                                    <i:Interaction.Behaviors>
                                        <behaviors:ButtonDragBehavior/>
                                    </i:Interaction.Behaviors>
                                    <StackPanel>
                                        <Border Background="#E3F2FD" CornerRadius="4" Padding="8" Margin="0,0,0,4">
                                            <Path Data="M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M19,5V19H5V5H19Z"
                                                  Fill="#1976D2" Width="24" Height="24" Stretch="Uniform"/>
                                        </Border>
                                        <TextBlock Text="设备看板" FontSize="11" FontWeight="Medium" HorizontalAlignment="Center" Foreground="#495057"/>
                                    </StackPanel>
                                </Button>
                            </StackPanel>
                        </Expander>

                        <!-- 结构组件分组 -->
                        <Expander Header="结构组件" IsExpanded="True" Margin="0,0,0,8">
                            <Expander.HeaderTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <Path Data="M12,2L2,7L12,12L22,7L12,2M2,17L12,22L22,17L12,12L2,17Z"
                                              Fill="#6F42C1" Width="14" Height="14" Stretch="Uniform" VerticalAlignment="Center"/>
                                        <TextBlock Text="{Binding}" FontWeight="Medium" FontSize="12" Foreground="#495057" Margin="6,0,0,0"/>
                                    </StackPanel>
                                </DataTemplate>
                            </Expander.HeaderTemplate>
                            <StackPanel Margin="0,8,0,0">
                                <!-- 墙体 -->
                                <Button Style="{StaticResource ToolboxButtonStyle}" Tag="{Binding ToolboxComponents[1]}" Click="ToolboxButton_Click">
                                    <i:Interaction.Behaviors>
                                        <behaviors:ButtonDragBehavior/>
                                    </i:Interaction.Behaviors>
                                    <StackPanel>
                                        <Border Background="#F3E5F5" CornerRadius="4" Padding="8" Margin="0,0,0,4">
                                            <Rectangle Fill="#7B1FA2" Width="24" Height="8" RadiusX="2" RadiusY="2"/>
                                        </Border>
                                        <TextBlock Text="墙体" FontSize="11" FontWeight="Medium" HorizontalAlignment="Center" Foreground="#495057"/>
                                    </StackPanel>
                                </Button>

                                <!-- 区域 -->
                                <Button Style="{StaticResource ToolboxButtonStyle}" Tag="{Binding ToolboxComponents[2]}" Click="ToolboxButton_Click">
                                    <i:Interaction.Behaviors>
                                        <behaviors:ButtonDragBehavior/>
                                    </i:Interaction.Behaviors>
                                    <StackPanel>
                                        <Border Background="#E8F5E8" CornerRadius="4" Padding="8" Margin="0,0,0,4">
                                            <Border BorderBrush="#4CAF50" BorderThickness="2" CornerRadius="4" Width="24" Height="18">
                                                <TextBlock Text="区域" FontSize="8" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="#4CAF50"/>
                                            </Border>
                                        </Border>
                                        <TextBlock Text="区域" FontSize="11" FontWeight="Medium" HorizontalAlignment="Center" Foreground="#495057"/>
                                    </StackPanel>
                                </Button>
                            </StackPanel>
                        </Expander>

                        <!-- 指示组件分组 -->
                        <Expander Header="指示组件" IsExpanded="True">
                            <Expander.HeaderTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <Path Data="M12,2A7,7 0 0,1 19,9C19,11.38 17.81,13.47 16,14.74V17A1,1 0 0,1 15,18H9A1,1 0 0,1 8,17V14.74C6.19,13.47 5,11.38 5,9A7,7 0 0,1 12,2M9,21V20H15V21A1,1 0 0,1 14,22H10A1,1 0 0,1 9,21M12,4A5,5 0 0,0 7,9C7,11.05 8.23,12.81 10,13.58V16H14V13.58C15.77,12.81 17,11.05 17,9A5,5 0 0,0 12,4Z"
                                              Fill="#FFC107" Width="14" Height="14" Stretch="Uniform" VerticalAlignment="Center"/>
                                        <TextBlock Text="{Binding}" FontWeight="Medium" FontSize="12" Foreground="#495057" Margin="6,0,0,0"/>
                                    </StackPanel>
                                </DataTemplate>
                            </Expander.HeaderTemplate>
                            <StackPanel Margin="0,8,0,0">
                                <!-- 报警灯 -->
                                <Button Style="{StaticResource ToolboxButtonStyle}" Tag="{Binding ToolboxComponents[3]}" Click="ToolboxButton_Click">
                                    <i:Interaction.Behaviors>
                                        <behaviors:ButtonDragBehavior/>
                                    </i:Interaction.Behaviors>
                                    <StackPanel>
                                        <Border Background="#FFF3E0" CornerRadius="4" Padding="8" Margin="0,0,0,4">
                                            <Ellipse Fill="#FF9800" Width="24" Height="24">
                                                <Ellipse.Effect>
                                                    <DropShadowEffect Color="#FF9800" ShadowDepth="0" BlurRadius="4" Opacity="0.6"/>
                                                </Ellipse.Effect>
                                            </Ellipse>
                                        </Border>
                                        <TextBlock Text="报警灯" FontSize="11" FontWeight="Medium" HorizontalAlignment="Center" Foreground="#495057"/>
                                    </StackPanel>
                                </Button>
                            </StackPanel>
                        </Expander>
                    </StackPanel>
                </ScrollViewer>
            </DockPanel>

            <!-- 分隔符 -->
            <GridSplitter Grid.Column="1" Width="5" Background="Gray"
                         HorizontalAlignment="Stretch"/>

            <!-- 中间画布区域 -->
            <Border Grid.Column="2" Background="White" BorderBrush="Gray" BorderThickness="1">
                <ScrollViewer x:Name="CanvasScrollViewer"
                             HorizontalScrollBarVisibility="Auto"
                             VerticalScrollBarVisibility="Auto"
                             PanningMode="None">
                    <Border x:Name="CanvasContainer" ClipToBounds="True">
                        <Border.RenderTransform>
                            <TransformGroup>
                                <ScaleTransform ScaleX="{Binding ZoomFactor}" ScaleY="{Binding ZoomFactor}"/>
                                <TranslateTransform X="{Binding CanvasTranslateX}" Y="{Binding CanvasTranslateY}"/>
                            </TransformGroup>
                        </Border.RenderTransform>
                        <Canvas x:Name="MainCanvas"
                               Width="2000" Height="1500"
                               ClipToBounds="True">
                            <i:Interaction.Behaviors>
                                <behaviors:CanvasDropBehavior/>
                            </i:Interaction.Behaviors>

                        <!-- 网格背景 -->
                        <Canvas.Style>
                            <Style TargetType="Canvas">
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding ShowGrid}" Value="True">
                                        <Setter Property="Background">
                                            <Setter.Value>
                                                <DrawingBrush TileMode="Tile" Viewport="0,0,20,20" ViewportUnits="Absolute">
                                                    <DrawingBrush.Drawing>
                                                        <GeometryDrawing>
                                                            <GeometryDrawing.Geometry>
                                                                <RectangleGeometry Rect="0,0,20,20"/>
                                                            </GeometryDrawing.Geometry>
                                                            <GeometryDrawing.Pen>
                                                                <Pen Brush="LightGray" Thickness="0.5"/>
                                                            </GeometryDrawing.Pen>
                                                        </GeometryDrawing>
                                                    </DrawingBrush.Drawing>
                                                </DrawingBrush>
                                            </Setter.Value>
                                        </Setter>
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding ShowGrid}" Value="False">
                                        <Setter Property="Background" Value="White"/>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </Canvas.Style>

                        <!-- 画布组件 -->
                        <ItemsControl ItemsSource="{Binding CanvasComponents}">
                            <ItemsControl.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <Canvas/>
                                </ItemsPanelTemplate>
                            </ItemsControl.ItemsPanel>

                            <ItemsControl.ItemContainerStyle>
                                <Style TargetType="ContentPresenter">
                                    <Setter Property="Canvas.Left" Value="{Binding X}"/>
                                    <Setter Property="Canvas.Top" Value="{Binding Y}"/>
                                </Style>
                            </ItemsControl.ItemContainerStyle>

                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <controls:ResizableComponent DataContext="{Binding}"/>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                        </Canvas>
                    </Border>
                </ScrollViewer>
            </Border>

            <!-- 分隔符 -->
            <GridSplitter Grid.Column="3" Width="5" Background="Gray"
                         HorizontalAlignment="Stretch"/>

            <!-- 右侧属性面板 -->
            <Border Grid.Column="4" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="1">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="200"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="属性面板" FontWeight="Bold"
                              Margin="10" HorizontalAlignment="Center"/>

                    <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                        <ContentPresenter Content="{Binding SelectedComponent}"
                                        Margin="10">
                            <ContentPresenter.Resources>
                                <!-- 设备看板属性编辑模板 -->
                                <DataTemplate DataType="{x:Type models:DeviceBoard}">
                                    <StackPanel>
                                        <TextBlock Text="设备看板属性" FontWeight="Bold" Margin="0,0,0,10"/>

                                        <TextBlock Text="基本属性" FontWeight="Bold" Margin="0,5"/>
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            <Grid.RowDefinitions>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                            </Grid.RowDefinitions>

                                            <TextBlock Grid.Row="0" Grid.Column="0" Text="X:" Margin="0,5"/>
                                            <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding X}" Margin="5"/>

                                            <TextBlock Grid.Row="1" Grid.Column="0" Text="Y:" Margin="0,5"/>
                                            <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding Y}" Margin="5"/>

                                            <TextBlock Grid.Row="2" Grid.Column="0" Text="宽度:" Margin="0,5"/>
                                            <TextBox Grid.Row="2" Grid.Column="1" Text="{Binding Width}" Margin="5"/>

                                            <TextBlock Grid.Row="3" Grid.Column="0" Text="高度:" Margin="0,5"/>
                                            <TextBox Grid.Row="3" Grid.Column="1" Text="{Binding Height}" Margin="5"/>

                                            <TextBlock Grid.Row="4" Grid.Column="0" Text="名称:" Margin="0,5"/>
                                            <TextBox Grid.Row="4" Grid.Column="1" Text="{Binding Name}" Margin="5"/>
                                        </Grid>

                                        <TextBlock Text="设备属性" FontWeight="Bold" Margin="0,15,0,5"/>
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            <Grid.RowDefinitions>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                            </Grid.RowDefinitions>

                                            <TextBlock Grid.Row="0" Grid.Column="0" Text="设备名称:" Margin="0,5"/>
                                            <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding DeviceName}" Margin="5"/>

                                            <TextBlock Grid.Row="1" Grid.Column="0" Text="设备类型:" Margin="0,5"/>
                                            <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding DeviceType}" Margin="5"/>

                                            <TextBlock Grid.Row="2" Grid.Column="0" Text="位置:" Margin="0,5"/>
                                            <TextBox Grid.Row="2" Grid.Column="1" Text="{Binding Location}" Margin="5"/>

                                            <TextBlock Grid.Row="3" Grid.Column="0" Text="批次号:" Margin="0,5"/>
                                            <TextBox Grid.Row="3" Grid.Column="1" Text="{Binding BatchNumber}" Margin="5"/>

                                            <TextBlock Grid.Row="4" Grid.Column="0" Text="温度:" Margin="0,5"/>
                                            <TextBox Grid.Row="4" Grid.Column="1" Text="{Binding Temperature}" Margin="5"/>

                                            <TextBlock Grid.Row="5" Grid.Column="0" Text="湿度:" Margin="0,5"/>
                                            <TextBox Grid.Row="5" Grid.Column="1" Text="{Binding Humidity}" Margin="5"/>

                                            <TextBlock Grid.Row="6" Grid.Column="0" Text="状态:" Margin="0,5"/>
                                            <ComboBox Grid.Row="6" Grid.Column="1" SelectedItem="{Binding Status}" Margin="5">
                                                <ComboBox.Items>
                                                    <models:DeviceStatus>Normal</models:DeviceStatus>
                                                    <models:DeviceStatus>Warning</models:DeviceStatus>
                                                    <models:DeviceStatus>Error</models:DeviceStatus>
                                                    <models:DeviceStatus>Offline</models:DeviceStatus>
                                                </ComboBox.Items>
                                            </ComboBox>
                                        </Grid>
                                    </StackPanel>
                                </DataTemplate>

                                <!-- 墙体属性编辑模板 -->
                                <DataTemplate DataType="{x:Type models:Wall}">
                                    <StackPanel>
                                        <TextBlock Text="墙体属性" FontWeight="Bold" Margin="0,0,0,10"/>

                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            <Grid.RowDefinitions>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                            </Grid.RowDefinitions>

                                            <TextBlock Grid.Row="0" Grid.Column="0" Text="X:" Margin="0,5"/>
                                            <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding X}" Margin="5"/>

                                            <TextBlock Grid.Row="1" Grid.Column="0" Text="Y:" Margin="0,5"/>
                                            <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding Y}" Margin="5"/>

                                            <TextBlock Grid.Row="2" Grid.Column="0" Text="宽度:" Margin="0,5"/>
                                            <TextBox Grid.Row="2" Grid.Column="1" Text="{Binding Width}" Margin="5"/>

                                            <TextBlock Grid.Row="3" Grid.Column="0" Text="高度:" Margin="0,5"/>
                                            <TextBox Grid.Row="3" Grid.Column="1" Text="{Binding Height}" Margin="5"/>

                                            <TextBlock Grid.Row="4" Grid.Column="0" Text="名称:" Margin="0,5"/>
                                            <TextBox Grid.Row="4" Grid.Column="1" Text="{Binding Name}" Margin="5"/>

                                            <TextBlock Grid.Row="5" Grid.Column="0" Text="边框厚度:" Margin="0,5"/>
                                            <TextBox Grid.Row="5" Grid.Column="1" Text="{Binding BorderThickness}" Margin="5"/>
                                        </Grid>
                                    </StackPanel>
                                </DataTemplate>

                                <!-- 区域属性编辑模板 -->
                                <DataTemplate DataType="{x:Type models:Area}">
                                    <StackPanel>
                                        <TextBlock Text="区域属性" FontWeight="Bold" Margin="0,0,0,10"/>

                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            <Grid.RowDefinitions>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                            </Grid.RowDefinitions>

                                            <TextBlock Grid.Row="0" Grid.Column="0" Text="X:" Margin="0,5"/>
                                            <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding X}" Margin="5"/>

                                            <TextBlock Grid.Row="1" Grid.Column="0" Text="Y:" Margin="0,5"/>
                                            <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding Y}" Margin="5"/>

                                            <TextBlock Grid.Row="2" Grid.Column="0" Text="宽度:" Margin="0,5"/>
                                            <TextBox Grid.Row="2" Grid.Column="1" Text="{Binding Width}" Margin="5"/>

                                            <TextBlock Grid.Row="3" Grid.Column="0" Text="高度:" Margin="0,5"/>
                                            <TextBox Grid.Row="3" Grid.Column="1" Text="{Binding Height}" Margin="5"/>

                                            <TextBlock Grid.Row="4" Grid.Column="0" Text="名称:" Margin="0,5"/>
                                            <TextBox Grid.Row="4" Grid.Column="1" Text="{Binding Name}" Margin="5"/>

                                            <TextBlock Grid.Row="5" Grid.Column="0" Text="区域名称:" Margin="0,5"/>
                                            <TextBox Grid.Row="5" Grid.Column="1" Text="{Binding AreaName}" Margin="5"/>

                                            <TextBlock Grid.Row="6" Grid.Column="0" Text="描述:" Margin="0,5"/>
                                            <TextBox Grid.Row="6" Grid.Column="1" Text="{Binding Description}" Margin="5" AcceptsReturn="True" Height="60"/>
                                        </Grid>
                                    </StackPanel>
                                </DataTemplate>

                                <!-- 报警灯属性编辑模板 -->
                                <DataTemplate DataType="{x:Type models:AlarmLight}">
                                    <StackPanel>
                                        <TextBlock Text="报警灯属性" FontWeight="Bold" Margin="0,0,0,10"/>

                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            <Grid.RowDefinitions>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                            </Grid.RowDefinitions>

                                            <TextBlock Grid.Row="0" Grid.Column="0" Text="X:" Margin="0,5"/>
                                            <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding X}" Margin="5"/>

                                            <TextBlock Grid.Row="1" Grid.Column="0" Text="Y:" Margin="0,5"/>
                                            <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding Y}" Margin="5"/>

                                            <TextBlock Grid.Row="2" Grid.Column="0" Text="宽度:" Margin="0,5"/>
                                            <TextBox Grid.Row="2" Grid.Column="1" Text="{Binding Width}" Margin="5"/>

                                            <TextBlock Grid.Row="3" Grid.Column="0" Text="高度:" Margin="0,5"/>
                                            <TextBox Grid.Row="3" Grid.Column="1" Text="{Binding Height}" Margin="5"/>

                                            <TextBlock Grid.Row="4" Grid.Column="0" Text="名称:" Margin="0,5"/>
                                            <TextBox Grid.Row="4" Grid.Column="1" Text="{Binding Name}" Margin="5"/>

                                            <TextBlock Grid.Row="5" Grid.Column="0" Text="报警状态:" Margin="0,5"/>
                                            <ComboBox Grid.Row="5" Grid.Column="1" SelectedItem="{Binding AlarmStatus}" Margin="5">
                                                <ComboBox.Items>
                                                    <models:AlarmStatus>Off</models:AlarmStatus>
                                                    <models:AlarmStatus>Normal</models:AlarmStatus>
                                                    <models:AlarmStatus>Warning</models:AlarmStatus>
                                                    <models:AlarmStatus>Alarm</models:AlarmStatus>
                                                </ComboBox.Items>
                                            </ComboBox>

                                            <TextBlock Grid.Row="6" Grid.Column="0" Text="报警信息:" Margin="0,5"/>
                                            <TextBox Grid.Row="6" Grid.Column="1" Text="{Binding AlarmMessage}" Margin="5"/>
                                        </Grid>
                                    </StackPanel>
                                </DataTemplate>
                            </ContentPresenter.Resources>
                        </ContentPresenter>
                    </ScrollViewer>

                    <!-- 分隔符 -->
                    <GridSplitter Grid.Row="2" Height="5" Background="Gray"
                                 HorizontalAlignment="Stretch" VerticalAlignment="Center"/>

                    <!-- 小视图窗口 -->
                    <Border Grid.Row="3" Background="White" BorderBrush="Gray" BorderThickness="1" Margin="5">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Text="导航视图" FontWeight="Bold"
                                      Margin="5" HorizontalAlignment="Center" FontSize="11"/>

                            <Border Grid.Row="1" Background="White" BorderBrush="DarkGray" BorderThickness="1" Margin="5">
                                <Viewbox Stretch="Uniform" StretchDirection="DownOnly">
                                    <Border Width="200" Height="150" Background="LightGray">
                                        <!-- 缩略图画布 -->
                                        <Canvas x:Name="ThumbnailCanvas"
                                               Width="200" Height="150"
                                               ClipToBounds="True">
                                            <!-- 网格背景 -->
                                            <Canvas.Background>
                                                <DrawingBrush TileMode="Tile" Viewport="0,0,10,10" ViewportUnits="Absolute">
                                                    <DrawingBrush.Drawing>
                                                        <GeometryDrawing>
                                                            <GeometryDrawing.Geometry>
                                                                <RectangleGeometry Rect="0,0,10,10"/>
                                                            </GeometryDrawing.Geometry>
                                                            <GeometryDrawing.Pen>
                                                                <Pen Brush="LightGray" Thickness="0.25"/>
                                                            </GeometryDrawing.Pen>
                                                        </GeometryDrawing>
                                                    </DrawingBrush.Drawing>
                                                </DrawingBrush>
                                            </Canvas.Background>

                                            <!-- 缩略图组件 -->
                                            <ItemsControl ItemsSource="{Binding CanvasComponents}">
                                                <ItemsControl.ItemsPanel>
                                                    <ItemsPanelTemplate>
                                                        <Canvas/>
                                                    </ItemsPanelTemplate>
                                                </ItemsControl.ItemsPanel>

                                                <ItemsControl.ItemContainerStyle>
                                                    <Style TargetType="ContentPresenter">
                                                        <Setter Property="Canvas.Left" Value="{Binding X, Converter={StaticResource ThumbnailScaleConverter}}"/>
                                                        <Setter Property="Canvas.Top" Value="{Binding Y, Converter={StaticResource ThumbnailScaleConverter}}"/>
                                                    </Style>
                                                </ItemsControl.ItemContainerStyle>

                                                <ItemsControl.ItemTemplate>
                                                    <DataTemplate>
                                                        <Rectangle Width="{Binding Width, Converter={StaticResource ThumbnailScaleConverter}}"
                                                                  Height="{Binding Height, Converter={StaticResource ThumbnailScaleConverter}}"
                                                                  Fill="Blue" Opacity="0.7" Stroke="DarkBlue" StrokeThickness="0.5"/>
                                                    </DataTemplate>
                                                </ItemsControl.ItemTemplate>
                                            </ItemsControl>

                                            <!-- 视口指示器 -->
                                            <Rectangle x:Name="ViewportIndicator"
                                                      Stroke="Red" StrokeThickness="1" Fill="Transparent"
                                                      Width="50" Height="37.5"
                                                      Canvas.Left="0" Canvas.Top="0"/>
                                        </Canvas>
                                    </Border>
                                </Viewbox>
                            </Border>
                        </Grid>
                    </Border>
                </Grid>
            </Border>
        </Grid>

        <!-- 状态栏 -->
        <StatusBar Grid.Row="2" Background="LightGray">
            <StatusBarItem>
                <TextBlock Text="就绪"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <TextBlock Text="{Binding CanvasComponents.Count, StringFormat='组件数量: {0}'}"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <TextBlock Text="{Binding SelectedComponent.DisplayName, StringFormat='选中: {0}'}"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <TextBlock Text="{Binding ZoomFactor, StringFormat='缩放: {0:P0}'}"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <TextBlock Text="{Binding ShowGrid, Converter={StaticResource BoolToGridStatusConverter}}"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
