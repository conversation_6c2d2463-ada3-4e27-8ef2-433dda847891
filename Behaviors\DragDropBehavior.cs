using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using Microsoft.Xaml.Behaviors;
using BoardDesigner.Models;

namespace BoardDesigner.Behaviors
{
    public class DragDropBehavior : Behavior<FrameworkElement>
    {
        private bool _isDragging;
        private Point _startPoint;

        protected override void OnAttached()
        {
            base.OnAttached();
            AssociatedObject.MouseLeftButtonDown += OnMouseLeftButtonDown;
            AssociatedObject.MouseMove += OnMouseMove;
            AssociatedObject.MouseLeftButtonUp += OnMouseLeftButtonUp;
        }

        protected override void OnDetaching()
        {
            AssociatedObject.MouseLeftButtonDown -= OnMouseLeftButtonDown;
            AssociatedObject.MouseMove -= OnMouseMove;
            AssociatedObject.MouseLeftButtonUp -= OnMouseLeftButtonUp;
            base.OnDetaching();
        }

        private void OnMouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            _startPoint = e.GetPosition(AssociatedObject);
            _isDragging = true;
            AssociatedObject.CaptureMouse();
        }

        private void OnMouseMove(object sender, MouseEventArgs e)
        {
            if (!_isDragging || e.LeftButton != MouseButtonState.Pressed) return;

            var currentPoint = e.GetPosition(AssociatedObject.Parent as UIElement);
            var deltaX = currentPoint.X - _startPoint.X;
            var deltaY = currentPoint.Y - _startPoint.Y;

            if (AssociatedObject.DataContext is ComponentBase component)
            {
                component.X = Math.Max(0, component.X + deltaX);
                component.Y = Math.Max(0, component.Y + deltaY);
                _startPoint = currentPoint;
            }
        }

        private void OnMouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            if (_isDragging)
            {
                _isDragging = false;
                AssociatedObject.ReleaseMouseCapture();
            }
        }
    }

    public class ToolboxDragBehavior : Behavior<ListBox>
    {
        protected override void OnAttached()
        {
            base.OnAttached();
            AssociatedObject.MouseMove += OnMouseMove;
        }

        protected override void OnDetaching()
        {
            AssociatedObject.MouseMove -= OnMouseMove;
            base.OnDetaching();
        }

        private void OnMouseMove(object sender, MouseEventArgs e)
        {
            if (e.LeftButton == MouseButtonState.Pressed && AssociatedObject.SelectedItem != null)
            {
                var dragData = new DataObject("ComponentTemplate", AssociatedObject.SelectedItem);
                DragDrop.DoDragDrop(AssociatedObject, dragData, DragDropEffects.Copy);
            }
        }
    }

    public class ButtonDragBehavior : Behavior<Button>
    {
        private bool _isDragging = false;
        private Point _startPoint;

        protected override void OnAttached()
        {
            base.OnAttached();
            AssociatedObject.MouseLeftButtonDown += OnMouseLeftButtonDown;
            AssociatedObject.MouseMove += OnMouseMove;
            AssociatedObject.MouseLeftButtonUp += OnMouseLeftButtonUp;
        }

        protected override void OnDetaching()
        {
            AssociatedObject.MouseLeftButtonDown -= OnMouseLeftButtonDown;
            AssociatedObject.MouseMove -= OnMouseMove;
            AssociatedObject.MouseLeftButtonUp -= OnMouseLeftButtonUp;
            base.OnDetaching();
        }

        private void OnMouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            _startPoint = e.GetPosition(AssociatedObject);
            _isDragging = false;
        }

        private void OnMouseMove(object sender, MouseEventArgs e)
        {
            if (e.LeftButton == MouseButtonState.Pressed && !_isDragging)
            {
                var currentPoint = e.GetPosition(AssociatedObject);
                var distance = Math.Abs(currentPoint.X - _startPoint.X) + Math.Abs(currentPoint.Y - _startPoint.Y);

                // 只有移动距离超过阈值才开始拖拽
                if (distance > 5 && AssociatedObject.Tag is ComponentBase template)
                {
                    _isDragging = true;
                    var dragData = new DataObject("ComponentTemplate", template);
                    DragDrop.DoDragDrop(AssociatedObject, dragData, DragDropEffects.Copy);
                    _isDragging = false;
                }
            }
        }

        private void OnMouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            _isDragging = false;
        }
    }

    public class CanvasDropBehavior : Behavior<Canvas>
    {
        protected override void OnAttached()
        {
            base.OnAttached();
            AssociatedObject.AllowDrop = true;
            AssociatedObject.DragOver += OnDragOver;
            AssociatedObject.Drop += OnDrop;
            AssociatedObject.DragLeave += OnDragLeave;
            AssociatedObject.MouseLeftButtonDown += OnCanvasMouseLeftButtonDown;
        }

        protected override void OnDetaching()
        {
            AssociatedObject.DragOver -= OnDragOver;
            AssociatedObject.Drop -= OnDrop;
            AssociatedObject.DragLeave -= OnDragLeave;
            AssociatedObject.MouseLeftButtonDown -= OnCanvasMouseLeftButtonDown;
            base.OnDetaching();
        }

        private void OnDragOver(object sender, DragEventArgs e)
        {
            if (e.Data.GetDataPresent("ComponentTemplate"))
            {
                e.Effects = DragDropEffects.Copy;

                // 可以在这里添加视觉反馈，比如改变鼠标光标
                AssociatedObject.Cursor = Cursors.Cross;
            }
            else
            {
                e.Effects = DragDropEffects.None;
                AssociatedObject.Cursor = Cursors.No;
            }
            e.Handled = true;
        }

        private void OnDragLeave(object sender, DragEventArgs e)
        {
            // 重置光标
            AssociatedObject.Cursor = Cursors.Arrow;
        }

        private void OnDrop(object sender, DragEventArgs e)
        {
            if (e.Data.GetDataPresent("ComponentTemplate"))
            {
                var template = e.Data.GetData("ComponentTemplate") as ComponentBase;
                if (template != null)
                {
                    // 通过MainWindow获取ViewModel
                    var mainWindow = Application.Current.MainWindow as MainWindow;
                    if (mainWindow?.DataContext is ViewModels.MainViewModel viewModel)
                    {
                        var dropPosition = e.GetPosition(AssociatedObject);

                        ComponentBase newComponent = template.ComponentType switch
                        {
                            "DeviceBoard" => new DeviceBoard(),
                            "Wall" => new Wall(),
                            "Area" => new Area(),
                            "AlarmLight" => new AlarmLight(),
                            _ => throw new ArgumentException("Unknown component type")
                        };

                        // 考虑画布的缩放和平移，计算实际位置
                        var actualX = dropPosition.X;
                        var actualY = dropPosition.Y;

                        // 确保组件不会超出画布边界
                        actualX = Math.Max(0, Math.Min(2000 - newComponent.Width, actualX - newComponent.Width / 2));
                        actualY = Math.Max(0, Math.Min(1500 - newComponent.Height, actualY - newComponent.Height / 2));

                        newComponent.X = actualX;
                        newComponent.Y = actualY;

                        viewModel.CanvasComponents.Add(newComponent);
                        viewModel.SelectedComponent = newComponent;

                        // 标记项目为已修改
                        viewModel.IsProjectModified = true;
                    }
                }
            }

            // 重置光标
            AssociatedObject.Cursor = Cursors.Arrow;
            e.Handled = true;
        }

        private void OnCanvasMouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            // 让MainWindow的Canvas事件处理器来处理所有的逻辑
            // 包括取消选中和拖拽画布
            // 不在这里处理任何逻辑，避免冲突
        }
    }
}
