using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Threading;
using BoardDesigner.ViewModels;
using BoardDesigner.Models;

namespace BoardDesigner
{
    public partial class MainWindow : Window
    {
        private bool _isDraggingCanvas = false;
        private Point _lastMousePosition;

        public MainWindow()
        {
            InitializeComponent();

            // 添加键盘快捷键
            this.KeyDown += MainWindow_KeyDown;

            // 添加画布事件处理 - 直接在Canvas上处理，而不是ScrollViewer
            var canvas = FindCanvas();
            if (canvas != null)
            {
                canvas.MouseWheel += Canvas_MouseWheel;
                canvas.MouseLeftButtonDown += Canvas_MouseLeftButtonDown;
                canvas.MouseMove += Canvas_MouseMove;
                canvas.MouseLeftButtonUp += Canvas_MouseLeftButtonUp;
                canvas.MouseRightButtonDown += Canvas_MouseRightButtonDown;
                canvas.MouseRightButtonUp += Canvas_MouseRightButtonUp;
            }

            // 监听ScrollViewer大小变化以更新视口指示器
            CanvasScrollViewer.SizeChanged += CanvasScrollViewer_SizeChanged;
        }

        private Canvas? FindCanvas()
        {
            // 查找Canvas控件
            return FindName("MainCanvas") as Canvas ??
                   CanvasContainer?.Child as Canvas;
        }

        private void CanvasScrollViewer_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            // 当ScrollViewer大小变化时，更新ViewModel中的视口大小信息
            if (DataContext is MainViewModel viewModel)
            {
                // 这里可以添加一个方法来更新视口大小
                // 暂时先触发视口指示器更新
                viewModel.UpdateViewportSize(e.NewSize.Width, e.NewSize.Height);
            }
        }

        private void MainWindow_KeyDown(object sender, KeyEventArgs e)
        {
            if (DataContext is MainViewModel viewModel)
            {
                // Ctrl+N - 新建
                if (e.Key == Key.N && Keyboard.Modifiers == ModifierKeys.Control)
                {
                    viewModel.NewProjectCommand.Execute(null);
                    e.Handled = true;
                }
                // Ctrl+O - 打开
                else if (e.Key == Key.O && Keyboard.Modifiers == ModifierKeys.Control)
                {
                    viewModel.OpenProjectCommand.Execute(null);
                    e.Handled = true;
                }
                // Ctrl+S - 保存
                else if (e.Key == Key.S && Keyboard.Modifiers == ModifierKeys.Control)
                {
                    viewModel.SaveProjectCommand.Execute(null);
                    e.Handled = true;
                }
                // Ctrl+C - 复制
                else if (e.Key == Key.C && Keyboard.Modifiers == ModifierKeys.Control)
                {
                    viewModel.CopyComponentCommand.Execute(null);
                    e.Handled = true;
                }
                // Ctrl+V - 粘贴
                else if (e.Key == Key.V && Keyboard.Modifiers == ModifierKeys.Control)
                {
                    viewModel.PasteComponentCommand.Execute(null);
                    e.Handled = true;
                }
                // Delete - 删除
                else if (e.Key == Key.Delete)
                {
                    viewModel.DeleteComponentCommand.Execute(null);
                    e.Handled = true;
                }
                // G - 切换网格显示
                else if (e.Key == Key.G)
                {
                    viewModel.ToggleGridCommand.Execute(null);
                    e.Handled = true;
                }
                // Ctrl+G - 网格对齐
                else if (e.Key == Key.G && Keyboard.Modifiers == ModifierKeys.Control)
                {
                    viewModel.AlignToGridCommand.Execute(null);
                    e.Handled = true;
                }
                // Escape - 取消选中
                else if (e.Key == Key.Escape)
                {
                    viewModel.DeselectComponentCommand.Execute(null);
                    e.Handled = true;
                }
                // Ctrl+Plus - 放大
                else if (e.Key == Key.OemPlus && Keyboard.Modifiers == ModifierKeys.Control)
                {
                    viewModel.ZoomInCommand.Execute(null);
                    e.Handled = true;
                }
                // Ctrl+Minus - 缩小
                else if (e.Key == Key.OemMinus && Keyboard.Modifiers == ModifierKeys.Control)
                {
                    viewModel.ZoomOutCommand.Execute(null);
                    e.Handled = true;
                }
                // Ctrl+0 - 重置缩放
                else if (e.Key == Key.D0 && Keyboard.Modifiers == ModifierKeys.Control)
                {
                    viewModel.ResetZoomCommand.Execute(null);
                    e.Handled = true;
                }
                // Ctrl+N - 新建项目
                else if (e.Key == Key.N && Keyboard.Modifiers == ModifierKeys.Control)
                {
                    viewModel.NewProjectCommand.Execute(null);
                    e.Handled = true;
                }
                // Ctrl+O - 打开项目
                else if (e.Key == Key.O && Keyboard.Modifiers == ModifierKeys.Control)
                {
                    viewModel.OpenProjectCommand.Execute(null);
                    e.Handled = true;
                }
                // Ctrl+S - 保存项目
                else if (e.Key == Key.S && Keyboard.Modifiers == ModifierKeys.Control)
                {
                    viewModel.SaveProjectCommand.Execute(null);
                    e.Handled = true;
                }
                // Ctrl+Shift+S - 另存为
                else if (e.Key == Key.S && Keyboard.Modifiers == (ModifierKeys.Control | ModifierKeys.Shift))
                {
                    viewModel.SaveProjectAsCommand.Execute(null);
                    e.Handled = true;
                }
            }
        }

        private void ToolboxButton_Click(object sender, RoutedEventArgs e)
        {
            // 注意：这个方法现在主要用于双击或者没有拖拽时的点击
            // 拖拽功能由ButtonDragBehavior处理
            if (sender is Button button && button.Tag is ComponentBase template)
            {
                if (DataContext is MainViewModel viewModel)
                {
                    // 在画布中心添加组件
                    ComponentBase newComponent = template.ComponentType switch
                    {
                        "DeviceBoard" => new DeviceBoard(),
                        "Wall" => new Wall(),
                        "Area" => new Area(),
                        "AlarmLight" => new AlarmLight(),
                        "Label" => new Models.Label(),
                        _ => throw new ArgumentException("Unknown component type")
                    };

                    // 在画布中心位置创建组件
                    newComponent.X = 1000 - newComponent.Width / 2;  // 画布中心X
                    newComponent.Y = 750 - newComponent.Height / 2;   // 画布中心Y

                    viewModel.CanvasComponents.Add(newComponent);
                    viewModel.SelectedComponent = newComponent;
                    viewModel.IsProjectModified = true;
                }
            }
        }

        private void Canvas_MouseWheel(object sender, MouseWheelEventArgs e)
        {
            if (DataContext is MainViewModel viewModel)
            {
                var canvas = sender as Canvas;
                var mousePosition = e.GetPosition(canvas);
                viewModel.ZoomAtPoint(e.Delta, mousePosition.X, mousePosition.Y);
                e.Handled = true;
            }
        }

        private void Canvas_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            var canvas = sender as Canvas;
            var mousePosition = e.GetPosition(canvas);

            // 检查是否点击在空白区域（没有组件）
            var hitTest = VisualTreeHelper.HitTest(canvas, mousePosition);

            // 只有当点击的确实是Canvas本身（背景）时才开始拖拽画布
            // 如果点击的是组件或组件的子元素，不处理
            if (hitTest?.VisualHit == canvas)
            {
                _isDraggingCanvas = true;
                _lastMousePosition = e.GetPosition(CanvasScrollViewer);
                canvas?.CaptureMouse();
                if (canvas != null)
                    canvas.Cursor = Cursors.Hand;
                e.Handled = true;

                // 取消选中当前组件
                if (DataContext is MainViewModel viewModel)
                {
                    viewModel.DeselectComponentCommand.Execute(null);
                }
            }
            // 如果点击的不是Canvas背景，不处理事件，让组件处理
        }

        private void Canvas_MouseMove(object sender, MouseEventArgs e)
        {
            if (_isDraggingCanvas && DataContext is MainViewModel viewModel)
            {
                var currentPosition = e.GetPosition(CanvasScrollViewer);
                var deltaX = currentPosition.X - _lastMousePosition.X;
                var deltaY = currentPosition.Y - _lastMousePosition.Y;

                viewModel.CanvasTranslateX += deltaX;
                viewModel.CanvasTranslateY += deltaY;

                _lastMousePosition = currentPosition;
                e.Handled = true;
            }
        }

        private void Canvas_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            if (_isDraggingCanvas)
            {
                _isDraggingCanvas = false;
                var canvas = sender as Canvas;
                canvas?.ReleaseMouseCapture();
                if (canvas != null)
                    canvas.Cursor = Cursors.Arrow;
                e.Handled = true;
            }
        }

        private void Canvas_MouseRightButtonDown(object sender, MouseButtonEventArgs e)
        {
            // 右键也可以拖拽画布
            var canvas = sender as Canvas;
            _isDraggingCanvas = true;
            _lastMousePosition = e.GetPosition(CanvasScrollViewer);
            canvas?.CaptureMouse();
            if (canvas != null)
                canvas.Cursor = Cursors.Hand;
            e.Handled = true;
        }

        private void Canvas_MouseRightButtonUp(object sender, MouseButtonEventArgs e)
        {
            if (_isDraggingCanvas)
            {
                _isDraggingCanvas = false;
                var canvas = sender as Canvas;
                canvas?.ReleaseMouseCapture();
                if (canvas != null)
                    canvas.Cursor = Cursors.Arrow;
                e.Handled = true;
            }
        }

        private void AddCustomProperty_Click(object sender, RoutedEventArgs e)
        {
            if (DataContext is MainViewModel viewModel && viewModel.SelectedComponent is DeviceBoard device)
            {
                var newProperty = new Models.CustomProperty($"属性{device.CustomProperties.Count + 1}", "默认值", "文本");
                device.CustomProperties.Add(newProperty);

                // 调试输出
                System.Diagnostics.Debug.WriteLine($"添加自定义属性: {newProperty.Name}, 当前属性数量: {device.CustomProperties.Count}");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("无法添加自定义属性 - 没有选中的设备");
            }
        }

        private void CustomPropertyEditor_DeleteRequested(object sender, Models.CustomProperty e)
        {
            if (DataContext is MainViewModel viewModel && viewModel.SelectedComponent is DeviceBoard device)
            {
                device.CustomProperties.Remove(e);
            }
        }

        private void DeleteCustomProperty_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Models.CustomProperty property)
            {
                if (DataContext is MainViewModel viewModel && viewModel.SelectedComponent is DeviceBoard device)
                {
                    device.CustomProperties.Remove(property);
                    System.Diagnostics.Debug.WriteLine($"删除自定义属性: {property.Name}, 剩余属性数量: {device.CustomProperties.Count}");
                }
            }
        }

        private void AddToDynamicProperties_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Models.CustomProperty property)
            {
                if (DataContext is MainViewModel viewModel && viewModel.SelectedComponent is DeviceBoard device)
                {
                    // 检查是否已经存在同名的动态属性
                    var existingProperty = device.DynamicProperties.FirstOrDefault(p => p.Name == property.Name);
                    if (existingProperty != null)
                    {
                        // 更新现有属性的值
                        existingProperty.Value = property.Value;
                        existingProperty.Type = property.Type;
                        System.Diagnostics.Debug.WriteLine($"更新动态属性: {property.Name} = {property.Value}");
                    }
                    else
                    {
                        // 添加新的动态属性
                        var dynamicProperty = new Models.CustomProperty(property.Name, property.Value, property.Type);
                        device.DynamicProperties.Add(dynamicProperty);
                        System.Diagnostics.Debug.WriteLine($"添加动态属性: {property.Name} = {property.Value}");
                    }
                }
            }
        }

        private void RemoveDynamicProperty_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Models.CustomProperty property)
            {
                if (DataContext is MainViewModel viewModel && viewModel.SelectedComponent is DeviceBoard device)
                {
                    device.DynamicProperties.Remove(property);
                    System.Diagnostics.Debug.WriteLine($"移除动态属性: {property.Name}");
                }
            }
        }

        private void AddNewCustomProperty_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("=== AddNewCustomProperty_Click 开始 ===");

                var viewModel = DataContext as MainViewModel;
                System.Diagnostics.Debug.WriteLine($"ViewModel: {viewModel != null}");

                if (viewModel == null)
                {
                    System.Diagnostics.Debug.WriteLine("ViewModel 为 null");
                    return;
                }

                var selectedComponent = viewModel.SelectedComponent;
                System.Diagnostics.Debug.WriteLine($"SelectedComponent: {selectedComponent?.GetType().Name}");

                if (selectedComponent is DeviceBoard device)
                {
                    System.Diagnostics.Debug.WriteLine($"设备存在");

                    // 查找DynamicPropertiesPanel
                    var dynamicPanel = FindChild<StackPanel>(this, "DynamicPropertiesPanel");
                    if (dynamicPanel == null)
                    {
                        System.Diagnostics.Debug.WriteLine("找不到DynamicPropertiesPanel");
                        return;
                    }

                    System.Diagnostics.Debug.WriteLine("找到DynamicPropertiesPanel");

                    // 先测试不使用ObservableCollection，直接创建UI
                    var propertyName = "测试属性" + (dynamicPanel.Children.Count + 1);
                    System.Diagnostics.Debug.WriteLine($"创建属性: {propertyName}");

                    // 创建UI元素
                    var grid = new Grid();
                    grid.Margin = new Thickness(0, 5, 0, 0);

                    // 定义列
                    grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
                    grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
                    grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
                    grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

                    // CheckBox
                    var checkBox = new CheckBox();
                    checkBox.IsChecked = true;
                    checkBox.VerticalAlignment = VerticalAlignment.Center;
                    checkBox.Margin = new Thickness(0, 0, 5, 0);
                    checkBox.ToolTip = "勾选后在设备组件上显示";
                    Grid.SetColumn(checkBox, 0);
                    grid.Children.Add(checkBox);

                    // 属性名
                    var nameTextBlock = new TextBlock();
                    nameTextBlock.Text = propertyName + ":";
                    nameTextBlock.VerticalAlignment = VerticalAlignment.Center;
                    nameTextBlock.Margin = new Thickness(0, 0, 5, 0);
                    Grid.SetColumn(nameTextBlock, 1);
                    grid.Children.Add(nameTextBlock);

                    // 属性值
                    var valueTextBox = new TextBox();
                    valueTextBox.Text = "测试值";
                    valueTextBox.Height = 24;
                    valueTextBox.Margin = new Thickness(0, 0, 5, 0);
                    Grid.SetColumn(valueTextBox, 2);
                    grid.Children.Add(valueTextBox);

                    // 删除按钮
                    var deleteButton = new Button();
                    deleteButton.Content = "×";
                    deleteButton.Width = 20;
                    deleteButton.Height = 20;
                    deleteButton.Background = new SolidColorBrush(Color.FromRgb(255, 107, 107));
                    deleteButton.Foreground = Brushes.White;
                    deleteButton.BorderThickness = new Thickness(0);
                    deleteButton.FontSize = 12;
                    deleteButton.FontWeight = FontWeights.Bold;
                    Grid.SetColumn(deleteButton, 3);
                    grid.Children.Add(deleteButton);

                    // 添加到面板
                    dynamicPanel.Children.Add(grid);

                    System.Diagnostics.Debug.WriteLine($"UI元素添加成功，当前子元素数量: {dynamicPanel.Children.Count}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("选中的组件不是DeviceBoard类型");
                }

                System.Diagnostics.Debug.WriteLine("=== AddNewCustomProperty_Click 结束 ===");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"=== 发生异常 ===");
                System.Diagnostics.Debug.WriteLine($"异常类型: {ex.GetType().Name}");
                System.Diagnostics.Debug.WriteLine($"异常消息: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                if (ex.InnerException != null)
                {
                    System.Diagnostics.Debug.WriteLine($"内部异常: {ex.InnerException.Message}");
                }
            }
        }

        // 辅助方法：在可视化树中查找指定名称的子控件
        private T FindChild<T>(DependencyObject parent, string childName) where T : DependencyObject
        {
            if (parent == null) return null;

            T foundChild = null;

            int childrenCount = VisualTreeHelper.GetChildrenCount(parent);
            for (int i = 0; i < childrenCount; i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);

                if (child is T && (child as FrameworkElement)?.Name == childName)
                {
                    foundChild = (T)child;
                    break;
                }
                else
                {
                    foundChild = FindChild<T>(child, childName);
                    if (foundChild != null) break;
                }
            }

            return foundChild;
        }
    }
}
