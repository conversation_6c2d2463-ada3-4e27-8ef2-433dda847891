using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Threading;
using BoardDesigner.ViewModels;
using BoardDesigner.Models;

namespace BoardDesigner
{
    public partial class MainWindow : Window
    {
        private bool _isPanning = false;
        private Point _lastPanPoint;
        private bool _isLeftButtonDown = false;
        private Point _leftButtonDownPoint;
        private DispatcherTimer _longPressTimer;
        private bool _isLongPress = false;
        private const double DRAG_THRESHOLD = 5.0; // 像素阈值，超过此距离才开始拖拽

        public MainWindow()
        {
            InitializeComponent();

            // 初始化长按定时器
            _longPressTimer = new DispatcherTimer();
            _longPressTimer.Interval = TimeSpan.FromMilliseconds(300); // 300ms后认为是长按
            _longPressTimer.Tick += LongPressTimer_Tick;

            // 添加键盘快捷键
            this.KeyDown += MainWindow_KeyDown;

            // 添加画布事件处理
            CanvasScrollViewer.MouseWheel += CanvasScrollViewer_MouseWheel;
            CanvasScrollViewer.MouseLeftButtonDown += CanvasScrollViewer_MouseLeftButtonDown;
            CanvasScrollViewer.MouseMove += CanvasScrollViewer_MouseMove;
            CanvasScrollViewer.MouseLeftButtonUp += CanvasScrollViewer_MouseLeftButtonUp;
            CanvasScrollViewer.MouseRightButtonDown += CanvasScrollViewer_MouseRightButtonDown;
            CanvasScrollViewer.MouseRightButtonUp += CanvasScrollViewer_MouseRightButtonUp;
        }

        private void MainWindow_KeyDown(object sender, KeyEventArgs e)
        {
            if (DataContext is MainViewModel viewModel)
            {
                // Ctrl+N - 新建
                if (e.Key == Key.N && Keyboard.Modifiers == ModifierKeys.Control)
                {
                    viewModel.NewProjectCommand.Execute(null);
                    e.Handled = true;
                }
                // Ctrl+O - 打开
                else if (e.Key == Key.O && Keyboard.Modifiers == ModifierKeys.Control)
                {
                    viewModel.OpenProjectCommand.Execute(null);
                    e.Handled = true;
                }
                // Ctrl+S - 保存
                else if (e.Key == Key.S && Keyboard.Modifiers == ModifierKeys.Control)
                {
                    viewModel.SaveProjectCommand.Execute(null);
                    e.Handled = true;
                }
                // Ctrl+C - 复制
                else if (e.Key == Key.C && Keyboard.Modifiers == ModifierKeys.Control)
                {
                    viewModel.CopyComponentCommand.Execute(null);
                    e.Handled = true;
                }
                // Ctrl+V - 粘贴
                else if (e.Key == Key.V && Keyboard.Modifiers == ModifierKeys.Control)
                {
                    viewModel.PasteComponentCommand.Execute(null);
                    e.Handled = true;
                }
                // Delete - 删除
                else if (e.Key == Key.Delete)
                {
                    viewModel.DeleteComponentCommand.Execute(null);
                    e.Handled = true;
                }
                // G - 切换网格显示
                else if (e.Key == Key.G)
                {
                    viewModel.ToggleGridCommand.Execute(null);
                    e.Handled = true;
                }
                // Ctrl+G - 网格对齐
                else if (e.Key == Key.G && Keyboard.Modifiers == ModifierKeys.Control)
                {
                    viewModel.AlignToGridCommand.Execute(null);
                    e.Handled = true;
                }
                // Escape - 取消选中
                else if (e.Key == Key.Escape)
                {
                    viewModel.DeselectComponentCommand.Execute(null);
                    e.Handled = true;
                }
                // Ctrl+Plus - 放大
                else if (e.Key == Key.OemPlus && Keyboard.Modifiers == ModifierKeys.Control)
                {
                    viewModel.ZoomInCommand.Execute(null);
                    e.Handled = true;
                }
                // Ctrl+Minus - 缩小
                else if (e.Key == Key.OemMinus && Keyboard.Modifiers == ModifierKeys.Control)
                {
                    viewModel.ZoomOutCommand.Execute(null);
                    e.Handled = true;
                }
                // Ctrl+0 - 重置缩放
                else if (e.Key == Key.D0 && Keyboard.Modifiers == ModifierKeys.Control)
                {
                    viewModel.ResetZoomCommand.Execute(null);
                    e.Handled = true;
                }
            }
        }

        private void ToolboxButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is ComponentBase template)
            {
                if (DataContext is MainViewModel viewModel)
                {
                    viewModel.AddComponentCommand.Execute(template);
                }
            }
        }

        private void CanvasScrollViewer_MouseWheel(object sender, MouseWheelEventArgs e)
        {
            if (DataContext is MainViewModel viewModel)
            {
                var mousePosition = e.GetPosition(CanvasContainer);
                viewModel.ZoomAtPoint(e.Delta, mousePosition.X, mousePosition.Y);
                e.Handled = true;
            }
        }

        private void CanvasScrollViewer_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            // 检查是否按住了空格键或者右键，用于平移
            if (Keyboard.IsKeyDown(Key.Space))
            {
                _isPanning = true;
                _lastPanPoint = e.GetPosition(CanvasScrollViewer);
                CanvasScrollViewer.CaptureMouse();
                CanvasScrollViewer.Cursor = Cursors.Hand;
                e.Handled = true;
            }
        }

        private void CanvasScrollViewer_MouseRightButtonDown(object sender, MouseButtonEventArgs e)
        {
            _isPanning = true;
            _lastPanPoint = e.GetPosition(CanvasScrollViewer);
            CanvasScrollViewer.CaptureMouse();
            CanvasScrollViewer.Cursor = Cursors.Hand;
            e.Handled = true;
        }

        private void CanvasScrollViewer_MouseMove(object sender, MouseEventArgs e)
        {
            if (_isPanning && DataContext is MainViewModel viewModel)
            {
                var currentPoint = e.GetPosition(CanvasScrollViewer);
                var deltaX = currentPoint.X - _lastPanPoint.X;
                var deltaY = currentPoint.Y - _lastPanPoint.Y;

                viewModel.CanvasTranslateX += deltaX;
                viewModel.CanvasTranslateY += deltaY;

                _lastPanPoint = currentPoint;
                e.Handled = true;
            }
        }

        private void CanvasScrollViewer_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            if (_isPanning)
            {
                _isPanning = false;
                CanvasScrollViewer.ReleaseMouseCapture();
                CanvasScrollViewer.Cursor = Cursors.Arrow;
                e.Handled = true;
            }
        }

        private void CanvasScrollViewer_MouseRightButtonUp(object sender, MouseButtonEventArgs e)
        {
            if (_isPanning)
            {
                _isPanning = false;
                CanvasScrollViewer.ReleaseMouseCapture();
                CanvasScrollViewer.Cursor = Cursors.Arrow;
                e.Handled = true;
            }
        }
    }
}
