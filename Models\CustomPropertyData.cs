namespace BoardDesigner.Models
{
    /// <summary>
    /// 自定义属性数据模型，用于序列化
    /// </summary>
    public class CustomPropertyData
    {
        public string Name { get; set; } = "";
        public string Value { get; set; } = "";
        public string Type { get; set; } = "文本";

        public CustomPropertyData()
        {
        }

        public CustomPropertyData(string name, string value, string type)
        {
            Name = name;
            Value = value;
            Type = type;
        }
    }
}
