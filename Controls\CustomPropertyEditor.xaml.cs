using System;
using System.Windows;
using System.Windows.Controls;
using BoardDesigner.Models;

namespace BoardDesigner.Controls
{
    public partial class CustomPropertyEditor : UserControl
    {
        public static readonly DependencyProperty CustomPropertyProperty =
            DependencyProperty.Register(nameof(CustomProperty), typeof(CustomProperty), typeof(CustomPropertyEditor));

        public CustomProperty CustomProperty
        {
            get => (CustomProperty)GetValue(CustomPropertyProperty);
            set => SetValue(CustomPropertyProperty, value);
        }

        public event EventHandler<CustomProperty>? DeleteRequested;

        public CustomPropertyEditor()
        {
            InitializeComponent();
            DataContextChanged += OnDataContextChanged;
        }

        private void OnDataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
        {
            if (DataContext is CustomProperty customProperty)
            {
                CustomProperty = customProperty;
            }
        }

        private void DeleteButton_Click(object sender, RoutedEventArgs e)
        {
            if (DataContext is CustomProperty customProperty)
            {
                DeleteRequested?.Invoke(this, customProperty);
            }
        }
    }
}
