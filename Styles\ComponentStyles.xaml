<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:models="clr-namespace:BoardDesigner.Models">

    <!-- 设备看板样式 -->
    <DataTemplate DataType="{x:Type models:DeviceBoard}">
        <Border Background="White"
                BorderBrush="{Binding StatusColor}"
                BorderThickness="2"
                CornerRadius="8"
                Width="{Binding Width}"
                Height="{Binding Height}">
            <Border.Effect>
                <DropShadowEffect ShadowDepth="3" BlurRadius="8" Opacity="0.2" Color="Gray"/>
            </Border.Effect>

            <Grid Margin="8">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- 状态指示器 -->
                <Border Grid.Row="0"
                        Background="{Binding StatusColor}"
                        Height="4"
                        CornerRadius="2"
                        Margin="0,0,0,8"/>

                <!-- 设备名称 -->
                <TextBlock Grid.Row="1"
                          Text="{Binding DeviceName}"
                          FontWeight="Bold"
                          FontSize="14"
                          HorizontalAlignment="Center"
                          Foreground="#333333"
                          Margin="0,0,0,8">
                    <TextBlock.Style>
                        <Style TargetType="TextBlock">
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding DeviceNameVisible}" Value="False">
                                    <Setter Property="Visibility" Value="Collapsed"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </TextBlock.Style>
                </TextBlock>

                <!-- 设备信息 -->
                <StackPanel Grid.Row="2">
                    <!-- 设备类型 -->
                    <TextBlock Text="{Binding DeviceType}"
                              FontSize="11"
                              HorizontalAlignment="Center"
                              Foreground="#666666"
                              Margin="0,0,0,4">
                        <TextBlock.Style>
                            <Style TargetType="TextBlock">
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding DeviceTypeVisible}" Value="False">
                                        <Setter Property="Visibility" Value="Collapsed"/>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </TextBlock.Style>
                    </TextBlock>

                    <!-- 位置 -->
                    <TextBlock Text="{Binding Location}"
                              FontSize="10"
                              HorizontalAlignment="Center"
                              Foreground="#888888"
                              Margin="0,0,0,6">
                        <TextBlock.Style>
                            <Style TargetType="TextBlock">
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding LocationVisible}" Value="False">
                                        <Setter Property="Visibility" Value="Collapsed"/>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </TextBlock.Style>
                    </TextBlock>

                    <!-- 温度湿度 -->
                    <Grid Margin="0,0,0,4">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- 温度 -->
                        <StackPanel Grid.Column="0"
                                   Orientation="Vertical"
                                   HorizontalAlignment="Center">
                            <StackPanel.Style>
                                <Style TargetType="StackPanel">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding TemperatureVisible}" Value="False">
                                            <Setter Property="Visibility" Value="Collapsed"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </StackPanel.Style>
                            <TextBlock Text="温度" FontSize="9" Foreground="#999999" HorizontalAlignment="Center"/>
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                <TextBlock Text="{Binding Temperature}" FontSize="12" FontWeight="Bold" Foreground="#333333"/>
                                <TextBlock Text="°C" FontSize="10" Foreground="#666666" Margin="1,0,0,0"/>
                            </StackPanel>
                        </StackPanel>

                        <!-- 湿度 -->
                        <StackPanel Grid.Column="1"
                                   Orientation="Vertical"
                                   HorizontalAlignment="Center">
                            <StackPanel.Style>
                                <Style TargetType="StackPanel">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding HumidityVisible}" Value="False">
                                            <Setter Property="Visibility" Value="Collapsed"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </StackPanel.Style>
                            <TextBlock Text="湿度" FontSize="9" Foreground="#999999" HorizontalAlignment="Center"/>
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                <TextBlock Text="{Binding Humidity}" FontSize="12" FontWeight="Bold" Foreground="#333333"/>
                                <TextBlock Text="%" FontSize="10" Foreground="#666666" Margin="1,0,0,0"/>
                            </StackPanel>
                        </StackPanel>
                    </Grid>

                    <!-- 动态属性 - 只显示IsVisible为true的属性 -->
                    <ItemsControl ItemsSource="{Binding DynamicProperties}">
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Vertical" HorizontalAlignment="Center" Margin="0,2"
                                           Visibility="{Binding IsVisible, Converter={StaticResource BooleanToVisibilityConverter}}">
                                    <TextBlock Text="{Binding Name}" FontSize="9" Foreground="#999999" HorizontalAlignment="Center"/>
                                    <TextBlock Text="{Binding Value}" FontSize="10" FontWeight="Bold" Foreground="#333333" HorizontalAlignment="Center"/>
                                </StackPanel>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </StackPanel>

                <!-- 批次信息 -->
                <Border Grid.Row="3"
                        Background="#F5F5F5"
                        CornerRadius="4"
                        Padding="6,3"
                        Margin="0,8,0,0">
                    <Border.Style>
                        <Style TargetType="Border">
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding BatchNumberVisible}" Value="False">
                                    <Setter Property="Visibility" Value="Collapsed"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </Border.Style>
                    <TextBlock Text="{Binding BatchNumber}"
                              FontSize="10"
                              HorizontalAlignment="Center"
                              Foreground="#666666"/>
                </Border>
            </Grid>
        </Border>
    </DataTemplate>

    <!-- 墙体样式 -->
    <DataTemplate DataType="{x:Type models:Wall}">
        <Border BorderBrush="{Binding BorderColor}"
                BorderThickness="{Binding BorderThickness}"
                Width="{Binding Width}"
                Height="{Binding Height}"
                CornerRadius="2">
            <Border.Effect>
                <DropShadowEffect ShadowDepth="2" BlurRadius="4" Opacity="0.3" Color="Gray"/>
            </Border.Effect>

            <!-- 墙体纹理效果 -->
            <Border.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                    <GradientStop Color="#E0E0E0" Offset="0"/>
                    <GradientStop Color="#CCCCCC" Offset="0.5"/>
                    <GradientStop Color="#B8B8B8" Offset="1"/>
                </LinearGradientBrush>
            </Border.Background>

            <!-- 墙体标识 -->
            <TextBlock Text="墙体"
                      HorizontalAlignment="Center"
                      VerticalAlignment="Center"
                      FontSize="10"
                      FontWeight="Bold"
                      Foreground="#666666"
                      Opacity="0.7"/>
        </Border>
    </DataTemplate>

    <!-- 区域样式 -->
    <DataTemplate DataType="{x:Type models:Area}">
        <Border Background="{Binding FillColor}"
                BorderBrush="{Binding BorderColor}"
                BorderThickness="{Binding BorderThickness}"
                Width="{Binding Width}"
                Height="{Binding Height}"
                CornerRadius="8">
            <Border.Effect>
                <DropShadowEffect ShadowDepth="1" BlurRadius="6" Opacity="0.2" Color="Gray"/>
            </Border.Effect>

            <Grid>
                <!-- 区域标题背景 -->
                <Border Background="{Binding BorderColor}"
                        Height="30"
                        VerticalAlignment="Top"
                        CornerRadius="8,8,0,0"
                        Opacity="0.8">
                    <TextBlock Text="{Binding AreaName}"
                              HorizontalAlignment="Center"
                              VerticalAlignment="Center"
                              FontWeight="Bold"
                              FontSize="12"
                              Foreground="White"/>
                </Border>

                <!-- 区域描述 -->
                <TextBlock Text="{Binding Description}"
                          HorizontalAlignment="Center"
                          VerticalAlignment="Center"
                          Margin="10,35,10,10"
                          FontSize="10"
                          Foreground="#666666"
                          TextWrapping="Wrap"
                          TextAlignment="Center"/>
            </Grid>
        </Border>
    </DataTemplate>

    <!-- 报警灯样式 -->
    <DataTemplate DataType="{x:Type models:AlarmLight}">
        <Grid Width="{Binding Width}" Height="{Binding Height}">
            <!-- 外圈金属边框 -->
            <Ellipse Fill="#333333"
                     Stroke="#666666"
                     StrokeThickness="2">
                <Ellipse.Effect>
                    <DropShadowEffect ShadowDepth="3" BlurRadius="6" Opacity="0.4" Color="Black"/>
                </Ellipse.Effect>
            </Ellipse>

            <!-- 主灯体 -->
            <Ellipse Fill="{Binding LightColor}"
                     Margin="4"
                     Stroke="#DDDDDD"
                     StrokeThickness="1"/>

            <!-- 高光效果 -->
            <Ellipse Margin="8">
                <Ellipse.Fill>
                    <RadialGradientBrush>
                        <GradientStop Color="White" Offset="0" />
                        <GradientStop Color="Transparent" Offset="0.7" />
                    </RadialGradientBrush>
                </Ellipse.Fill>
                <Ellipse.OpacityMask>
                    <RadialGradientBrush>
                        <GradientStop Color="White" Offset="0" />
                        <GradientStop Color="Transparent" Offset="1" />
                    </RadialGradientBrush>
                </Ellipse.OpacityMask>
            </Ellipse>

            <!-- 中心发光点 -->
            <Ellipse Fill="{Binding LightColor}"
                     Width="20" Height="20"
                     HorizontalAlignment="Center"
                     VerticalAlignment="Center"
                     Opacity="0.9">
                <Ellipse.Effect>
                    <BlurEffect Radius="3"/>
                </Ellipse.Effect>
            </Ellipse>

            <!-- 状态文字 -->
            <TextBlock Text="{Binding AlarmStatus}"
                      HorizontalAlignment="Center"
                      VerticalAlignment="Bottom"
                      FontSize="8"
                      FontWeight="Bold"
                      Foreground="#333333"
                      Margin="0,0,0,-15"/>
        </Grid>
    </DataTemplate>

    <!-- 标签样式 -->
    <DataTemplate DataType="{x:Type models:Label}">
        <Border Background="Transparent"
                BorderBrush="{Binding BorderColor}"
                BorderThickness="{Binding BorderThickness}"
                Width="{Binding Width}"
                Height="{Binding Height}"
                CornerRadius="2">
            <TextBlock Text="{Binding Text}"
                      FontSize="{Binding FontSize}"
                      Foreground="{Binding TextColor}"
                      FontFamily="{Binding FontFamily}"
                      VerticalAlignment="Center"
                      Margin="5,2">
                <TextBlock.Style>
                    <Style TargetType="TextBlock">
                        <Style.Triggers>
                            <!-- 文本对齐 -->
                            <DataTrigger Binding="{Binding TextAlignment}" Value="Left">
                                <Setter Property="HorizontalAlignment" Value="Left"/>
                                <Setter Property="TextAlignment" Value="Left"/>
                            </DataTrigger>
                            <DataTrigger Binding="{Binding TextAlignment}" Value="Center">
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                <Setter Property="TextAlignment" Value="Center"/>
                            </DataTrigger>
                            <DataTrigger Binding="{Binding TextAlignment}" Value="Right">
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                <Setter Property="TextAlignment" Value="Right"/>
                            </DataTrigger>

                            <!-- 粗体 -->
                            <DataTrigger Binding="{Binding IsBold}" Value="True">
                                <Setter Property="FontWeight" Value="Bold"/>
                            </DataTrigger>

                            <!-- 斜体 -->
                            <DataTrigger Binding="{Binding IsItalic}" Value="True">
                                <Setter Property="FontStyle" Value="Italic"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </TextBlock.Style>
            </TextBlock>
        </Border>
    </DataTemplate>

    <!-- 选中状态样式 -->
    <Style x:Key="SelectedComponentStyle" TargetType="ContentPresenter">
        <Style.Triggers>
            <DataTrigger Binding="{Binding IsSelected}" Value="True">
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect Color="Blue" ShadowDepth="0" BlurRadius="10"/>
                    </Setter.Value>
                </Setter>
            </DataTrigger>
        </Style.Triggers>
    </Style>

</ResourceDictionary>
