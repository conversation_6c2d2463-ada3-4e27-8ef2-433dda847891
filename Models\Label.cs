using System.Windows.Media;

namespace BoardDesigner.Models
{
    public class Label : ComponentBase
    {
        private string _text = "标签文本";
        private double _fontSize = 14;
        private Brush _textColor = Brushes.Black;
        private string _fontFamily = "Microsoft YaHei";
        private bool _isBold = false;
        private bool _isItalic = false;
        private TextAlignment _textAlignment = TextAlignment.Left;

        public override string ComponentType => "Label";
        public override string DisplayName => "标签";

        public string Text
        {
            get => _text;
            set => SetProperty(ref _text, value);
        }

        public double FontSize
        {
            get => _fontSize;
            set => SetProperty(ref _fontSize, value);
        }

        public Brush TextColor
        {
            get => _textColor;
            set => SetProperty(ref _textColor, value);
        }

        public string FontFamily
        {
            get => _fontFamily;
            set => SetProperty(ref _fontFamily, value);
        }

        public bool IsBold
        {
            get => _isBold;
            set => SetProperty(ref _isBold, value);
        }

        public bool IsItalic
        {
            get => _isItalic;
            set => SetProperty(ref _isItalic, value);
        }

        public TextAlignment TextAlignment
        {
            get => _textAlignment;
            set => SetProperty(ref _textAlignment, value);
        }

        public Label()
        {
            Width = 100;
            Height = 30;
            Name = "标签";
        }
    }

    public enum TextAlignment
    {
        Left,
        Center,
        Right
    }
}
