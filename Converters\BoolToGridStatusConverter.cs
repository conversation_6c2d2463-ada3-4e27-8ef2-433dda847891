using System;
using System.Globalization;
using System.Windows.Data;

namespace BoardDesigner.Converters
{
    public class BoolToGridStatusConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return boolValue ? "网格: 开" : "网格: 关";
            }
            return "网格: 关";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
