# 设备看板设计器 (BoardDesigner)

一个基于WPF的设备管理设计器应用程序，允许用户通过拖拽方式设计设备布局图。

## 功能特性

### 🎯 核心功能
- **左侧工具箱**: 包含可拖拽的设备组件
- **中间画布**: 支持拖拽布局的设计区域
- **右侧属性面板**: 实时编辑选中组件的属性

### 🔧 支持的组件类型

#### 1. 设备看板
- 显示设备状态（正常/警告/错误/离线）
- 设备属性（名称、类型、位置）
- 实时数据（温度、湿度）
- 批次信息
- 状态颜色指示

#### 2. 墙体
- 长方形墙体组件
- 可调整尺寸和边框样式
- 支持自定义颜色

#### 3. 区域
- 半透明区域标识
- 可添加区域名称和描述
- 支持自定义颜色和边框

#### 4. 报警灯
- 圆形报警指示器
- 多种状态（关闭/正常/警告/报警）
- 状态颜色变化
- 支持报警信息

## 🚀 使用方法

### 启动应用程序
```bash
dotnet run
```

### 基本操作
1. **添加组件**:
   - 点击左侧工具箱中的组件按钮
   - 或从工具箱拖拽组件到中间画布
2. **选择组件**: 点击画布上的组件进行选择（显示蓝色边框和调整手柄）
3. **取消选中**: 点击画布空白处或按Escape键取消选中
4. **移动组件**: 拖拽选中的组件到新位置
5. **调整大小**: 拖拽选中组件边框上的8个调整手柄来改变大小
6. **编辑属性**: 在右侧属性面板修改组件属性
7. **删除组件**: 选中组件后点击"删除"按钮或按Delete键
8. **缩放画布**: 使用鼠标滚轮进行缩放，或使用Ctrl+Plus/Minus快捷键
9. **平移画布**:
   - **按住左键拖拽**（在空白区域，类似AutoCAD）
   - 右键拖拽
10. **重置视图**: 使用Ctrl+0快捷键重置缩放和平移

### 🎯 完整功能列表

#### 📁 **文件操作**
- **新建项目** (Ctrl+N): 创建新的设计项目
- **打开项目** (Ctrl+O): 打开已保存的项目文件
- **保存项目** (Ctrl+S): 保存当前设计到文件

#### ✂️ **编辑操作**
- **选择组件**: 单击组件进行选择
- **取消选中** (Escape): 点击空白处或按Escape键取消选中
- **复制组件** (Ctrl+C): 复制选中的组件
- **粘贴组件** (Ctrl+V): 粘贴已复制的组件
- **删除组件** (Delete): 删除选中的组件
- **清空画布**: 清除所有组件

#### 🎨 **视图操作**
- **网格对齐** (Ctrl+G): 将选中组件对齐到20px网格
- **显示网格** (G): 切换网格显示/隐藏
- **可视化大小调整**: 选中组件后显示8个调整手柄
- **实时选中反馈**: 蓝色边框和调整手柄

#### 🎯 **组件功能**
- **现代化工具箱**: Expander分组、图标化按钮、拖拽支持
- **美化组件外观**: 全新设计的组件样式
- **精确位置控制**: 像素级精确的位置和大小调整

### ⌨️ **键盘快捷键**
| 快捷键 | 功能 |
|--------|------|
| Ctrl+N | 新建项目 |
| Ctrl+O | 打开项目 |
| Ctrl+S | 保存项目 |
| Ctrl+Shift+S | 另存为 |
| Ctrl+C | 复制组件 |
| Ctrl+V | 粘贴组件 |
| Delete | 删除选中组件 |
| Escape | 取消选中组件 |
| G | 切换网格显示 |
| Ctrl+G | 网格对齐 |
| Ctrl+Plus | 放大画布 |
| Ctrl+Minus | 缩小画布 |
| Ctrl+0 | 重置缩放和平移 |
| 滚轮 | 缩放画布 |
| 右键拖拽 | 平移画布 |
| 左键拖拽空白区域 | 平移画布 |

## 🏗️ 项目结构

```
BoardDesigner/
├── Models/                 # 数据模型
│   ├── ComponentBase.cs   # 组件基类
│   ├── DeviceBoard.cs     # 设备看板模型
│   ├── Wall.cs            # 墙体模型
│   ├── Area.cs            # 区域模型
│   └── AlarmLight.cs      # 报警灯模型
├── ViewModels/            # 视图模型
│   └── MainViewModel.cs   # 主视图模型
├── Controls/              # 自定义控件
│   ├── ResizableComponent.xaml    # 可调整大小的组件容器
│   └── ResizableComponent.xaml.cs # 容器逻辑代码
├── Behaviors/             # 行为类
│   └── DragDropBehavior.cs # 拖拽行为
├── Styles/                # 样式资源
│   └── ComponentStyles.xaml # 组件样式
├── MainWindow.xaml        # 主窗口界面
├── MainWindow.xaml.cs     # 主窗口代码
├── App.xaml              # 应用程序资源
└── BoardDesigner.csproj  # 项目文件
```

## 🎨 界面布局

### 左侧工具箱 (200px)
- **现代化分组设计**: 使用Expander控件分组显示
- **设备组件**: 设备看板（带图标和预览）
- **结构组件**: 墙体、区域（图标化显示）
- **指示组件**: 报警灯（发光效果预览）
- **拖拽添加**: 按住组件按钮拖拽到画布任意位置
- **点击添加**: 点击按钮在画布中心添加组件
- **视觉反馈**: 拖拽过程中显示十字光标，提供清晰的操作反馈

### 中间画布 (自适应)
- 2000x1500像素设计区域
- 20x20像素网格背景
- **滚轮缩放**: 使用鼠标滚轮进行缩放（0.1x - 5.0x）
- **智能左键交互**:
  - 点击组件：选择和拖拽组件
  - 点击空白区域：取消选中
  - 按住左键拖拽空白区域：平移画布（类似AutoCAD）
- **多种平移方式**: 按住左键拖拽、右键拖拽
- **滚动条支持**: 恢复传统滚动条，支持大画布导航
- **网格显示**: 20x20像素网格，可切换显示/隐藏
- 拖拽放置区域

### 右侧属性面板 (250px)
- 基本属性编辑（位置、尺寸、名称）
- 组件特定属性编辑
- 实时属性更新
- **导航视图窗口**: 显示整个画布的缩略图
  - 实时显示所有组件的位置
  - 红色框显示当前视口范围
  - 便于大型设计图的导航

## 🔧 技术栈

- **.NET 6.0**: 目标框架
- **WPF**: 用户界面框架
- **MVVM**: 架构模式
- **Microsoft.Xaml.Behaviors**: 行为支持
- **Data Binding**: 数据绑定
- **Drag & Drop**: 拖拽功能

## 📋 系统要求

- Windows 10/11
- .NET 6.0 Runtime
- Visual Studio 2022 (开发环境)

## 🚧 开发计划

### 已完成功能
- ✅ 基本拖拽功能
- ✅ 组件选择和属性编辑
- ✅ 四种基本组件类型（设备看板、墙体、区域、报警灯）
- ✅ 状态颜色指示
- ✅ 网格背景显示
- ✅ **可视化大小调整** - 8个调整手柄支持
- ✅ **美化组件外观** - 现代化设计风格
- ✅ **实时选中反馈** - 蓝色边框和调整手柄
- ✅ **精确位置控制** - 像素级精度
- ✅ **现代化工具箱** - Expander分组、图标化按钮、拖拽支持
- ✅ **完整菜单栏** - 文件操作、编辑操作、视图操作
- ✅ **键盘快捷键** - 全套快捷键支持
- ✅ **组件复制粘贴** - 支持组件的复制和粘贴
- ✅ **网格对齐功能** - 20px网格对齐
- ✅ **网格显示切换** - 可切换网格显示/隐藏
- ✅ **智能选择系统** - 单击选中，空白处取消选中，Escape键取消选中
- ✅ **画布缩放功能** - 滚轮缩放、键盘快捷键缩放（0.1x-5.0x）
- ✅ **智能画布平移** - 按住左键拖拽（类似AutoCAD）、右键拖拽
- ✅ **滚动条支持** - 恢复传统滚动条，支持大画布导航
- ✅ **网格显示功能** - 20x20像素网格，可切换显示/隐藏
- ✅ **导航视图窗口** - 缩略图导航，显示整个画布概览和当前视口
- ✅ **AutoCAD风格交互** - 参考AutoCAD的操作习惯，智能判断左键行为
- ✅ **完整的项目保存/加载** - JSON格式保存，支持所有组件属性和画布设置
- ✅ **智能文件管理** - 自动检测未保存更改，支持另存为功能
- ✅ **工具箱拖拽功能** - 从工具箱拖拽组件到画布任意位置
- ✅ **拖拽视觉反馈** - 拖拽过程中的光标变化和视觉提示

### 待开发功能
- 🔄 撤销/重做功能
- 🔄 组件分组
- 🔄 导出为图片
- 🔄 组件模板库
- 🔄 多页面支持
- 🔄 最近文件列表

## 📝 许可证

本项目采用 MIT 许可证。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

---

**注意**: 这是一个演示项目，展示了WPF中拖拽设计器的实现方法。
