<UserControl x:Class="BoardDesigner.Controls.CustomPropertyEditor"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:models="clr-namespace:BoardDesigner.Models">

    <Border BorderBrush="LightGray" BorderThickness="1" CornerRadius="4" Padding="8" Margin="0,4">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- 属性名称 -->
            <TextBlock Grid.Row="0" Grid.Column="0" Text="属性名:" VerticalAlignment="Center" Margin="0,0,5,0"/>
            <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding Name, UpdateSourceTrigger=PropertyChanged}" 
                     Margin="0,0,5,0" Height="24"/>
            
            <!-- 属性类型 -->
            <TextBlock Grid.Row="1" Grid.Column="0" Text="类型:" VerticalAlignment="Center" Margin="0,5,5,0"/>
            <ComboBox Grid.Row="1" Grid.Column="1" SelectedItem="{Binding Type}" Margin="0,5,5,0" Height="24">
                <ComboBoxItem>文本</ComboBoxItem>
                <ComboBoxItem>数字</ComboBoxItem>
                <ComboBoxItem>日期</ComboBoxItem>
                <ComboBoxItem>布尔</ComboBoxItem>
            </ComboBox>
            
            <!-- 属性值 -->
            <TextBlock Grid.Row="2" Grid.Column="0" Text="值:" VerticalAlignment="Center" Margin="0,5,5,0"/>
            <TextBox Grid.Row="2" Grid.Column="1" Text="{Binding Value, UpdateSourceTrigger=PropertyChanged}" 
                     Margin="0,5,5,0" Height="24"/>
            
            <!-- 删除按钮 -->
            <Button Grid.Row="0" Grid.Column="2" Grid.RowSpan="3" 
                    Content="删除" Width="50" Height="24"
                    Background="#FF6B6B" Foreground="White"
                    BorderThickness="0" CornerRadius="2"
                    VerticalAlignment="Top"
                    Click="DeleteButton_Click"/>
        </Grid>
    </Border>
</UserControl>
