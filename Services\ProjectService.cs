using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using System.Windows.Media;
using BoardDesigner.Models;

namespace BoardDesigner.Services
{
    public class ProjectService
    {
        private readonly JsonSerializerOptions _jsonOptions;

        public ProjectService()
        {
            _jsonOptions = new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                Converters = { new System.Text.Json.Serialization.JsonStringEnumConverter() }
            };
        }

        /// <summary>
        /// 保存项目到文件
        /// </summary>
        public async Task SaveProjectAsync(string filePath, ProjectData projectData)
        {
            try
            {
                projectData.LastModifiedTime = DateTime.Now;

                var json = JsonSerializer.Serialize(projectData, _jsonOptions);
                await File.WriteAllTextAsync(filePath, json);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"保存项目失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 从文件加载项目
        /// </summary>
        public async Task<ProjectData> LoadProjectAsync(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    throw new FileNotFoundException($"项目文件不存在: {filePath}");
                }

                var json = await File.ReadAllTextAsync(filePath);
                var projectData = JsonSerializer.Deserialize<ProjectData>(json, _jsonOptions);

                if (projectData == null)
                {
                    throw new InvalidOperationException("项目文件格式无效");
                }

                return projectData;
            }
            catch (JsonException ex)
            {
                throw new InvalidOperationException($"项目文件格式错误: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"加载项目失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 将组件模型转换为数据模型
        /// </summary>
        public object ConvertToComponentData(ComponentBase component)
        {
            return component.ComponentType switch
            {
                "DeviceBoard" => ConvertDeviceBoard((DeviceBoard)component),
                "Wall" => ConvertWall((Wall)component),
                "Area" => ConvertArea((Area)component),
                "AlarmLight" => ConvertAlarmLight((AlarmLight)component),
                "Label" => ConvertLabel((Label)component),
                _ => throw new ArgumentException($"未知的组件类型: {component.ComponentType}")
            };
        }

        /// <summary>
        /// 将数据模型转换为组件模型
        /// </summary>
        public ComponentBase ConvertToComponent(object data)
        {
            // 首先尝试反序列化为JsonElement，然后根据类型转换
            if (data is JsonElement element)
            {
                var componentType = element.GetProperty("componentType").GetString();
                object componentData = componentType switch
                {
                    "DeviceBoard" => JsonSerializer.Deserialize<DeviceBoardData>(element.GetRawText(), _jsonOptions)!,
                    "Wall" => JsonSerializer.Deserialize<WallData>(element.GetRawText(), _jsonOptions)!,
                    "Area" => JsonSerializer.Deserialize<AreaData>(element.GetRawText(), _jsonOptions)!,
                    "AlarmLight" => JsonSerializer.Deserialize<AlarmLightData>(element.GetRawText(), _jsonOptions)!,
                    "Label" => JsonSerializer.Deserialize<LabelData>(element.GetRawText(), _jsonOptions)!,
                    _ => throw new ArgumentException($"未知的组件类型: {componentType}")
                };

                // 递归调用以转换为ComponentBase
                return ConvertToComponent(componentData);
            }

            return data switch
            {
                DeviceBoardData deviceData => ConvertToDeviceBoard(deviceData),
                WallData wallData => ConvertToWall(wallData),
                AreaData areaData => ConvertToArea(areaData),
                AlarmLightData alarmData => ConvertToAlarmLight(alarmData),
                LabelData labelData => ConvertToLabel(labelData),
                _ => throw new ArgumentException($"未知的数据类型: {data.GetType()}")
            };
        }

        private DeviceBoardData ConvertDeviceBoard(DeviceBoard component)
        {
            return new DeviceBoardData
            {
                Id = component.Id,
                Name = component.Name,
                X = component.X,
                Y = component.Y,
                Width = component.Width,
                Height = component.Height,
                DeviceName = component.DeviceName,
                DeviceType = component.DeviceType,
                Location = component.Location,
                BatchNumber = component.BatchNumber,
                Temperature = component.Temperature,
                Humidity = component.Humidity,
                Status = component.Status,
                CustomProperties = component.CustomProperties.Select(cp => new CustomPropertyData(cp.Name, cp.Value, cp.Type)).ToList(),
                DynamicProperties = component.DynamicProperties.Select(dp => new CustomPropertyData(dp.Name, dp.Value, dp.Type)).ToList()
            };
        }

        private WallData ConvertWall(Wall component)
        {
            return new WallData
            {
                Id = component.Id,
                Name = component.Name,
                X = component.X,
                Y = component.Y,
                Width = component.Width,
                Height = component.Height,
                FillColor = BrushToString(component.FillColor),
                BorderColor = BrushToString(component.BorderColor),
                BorderThickness = component.BorderThickness
            };
        }

        private AreaData ConvertArea(Area component)
        {
            return new AreaData
            {
                Id = component.Id,
                Name = component.Name,
                X = component.X,
                Y = component.Y,
                Width = component.Width,
                Height = component.Height,
                AreaName = component.AreaName,
                Description = component.Description,
                FillColor = BrushToString(component.FillColor),
                BorderColor = BrushToString(component.BorderColor),
                BorderThickness = component.BorderThickness
            };
        }

        private AlarmLightData ConvertAlarmLight(AlarmLight component)
        {
            return new AlarmLightData
            {
                Id = component.Id,
                Name = component.Name,
                X = component.X,
                Y = component.Y,
                Width = component.Width,
                Height = component.Height,
                AlarmStatus = component.AlarmStatus,
                AlarmMessage = component.AlarmMessage,
                IsBlinking = component.IsBlinking
            };
        }

        private DeviceBoard ConvertToDeviceBoard(DeviceBoardData data)
        {
            var device = new DeviceBoard
            {
                Id = data.Id,
                Name = data.Name,
                X = data.X,
                Y = data.Y,
                Width = data.Width,
                Height = data.Height,
                DeviceName = data.DeviceName,
                DeviceType = data.DeviceType,
                Location = data.Location,
                BatchNumber = data.BatchNumber,
                Temperature = data.Temperature,
                Humidity = data.Humidity,
                Status = data.Status
            };

            // 清空默认的自定义属性，加载保存的属性
            device.CustomProperties.Clear();
            foreach (var propData in data.CustomProperties)
            {
                device.CustomProperties.Add(new CustomProperty(propData.Name, propData.Value, propData.Type));
            }

            // 加载动态属性
            device.DynamicProperties.Clear();
            foreach (var propData in data.DynamicProperties)
            {
                device.DynamicProperties.Add(new CustomProperty(propData.Name, propData.Value, propData.Type));
            }

            return device;
        }

        private Wall ConvertToWall(WallData data)
        {
            return new Wall
            {
                Id = data.Id,
                Name = data.Name,
                X = data.X,
                Y = data.Y,
                Width = data.Width,
                Height = data.Height,
                FillColor = StringToBrush(data.FillColor),
                BorderColor = StringToBrush(data.BorderColor),
                BorderThickness = data.BorderThickness
            };
        }

        private Area ConvertToArea(AreaData data)
        {
            return new Area
            {
                Id = data.Id,
                Name = data.Name,
                X = data.X,
                Y = data.Y,
                Width = data.Width,
                Height = data.Height,
                AreaName = data.AreaName,
                Description = data.Description,
                FillColor = StringToBrush(data.FillColor),
                BorderColor = StringToBrush(data.BorderColor),
                BorderThickness = data.BorderThickness
            };
        }

        private AlarmLight ConvertToAlarmLight(AlarmLightData data)
        {
            return new AlarmLight
            {
                Id = data.Id,
                Name = data.Name,
                X = data.X,
                Y = data.Y,
                Width = data.Width,
                Height = data.Height,
                AlarmStatus = data.AlarmStatus,
                AlarmMessage = data.AlarmMessage,
                IsBlinking = data.IsBlinking
            };
        }

        private LabelData ConvertLabel(Label component)
        {
            return new LabelData
            {
                Id = component.Id,
                Name = component.Name,
                X = component.X,
                Y = component.Y,
                Width = component.Width,
                Height = component.Height,
                Text = component.Text,
                FontSize = component.FontSize,
                TextColor = BrushToString(component.TextColor),
                FontFamily = component.FontFamily,
                IsBold = component.IsBold,
                IsItalic = component.IsItalic,
                TextAlignment = component.TextAlignment.ToString()
            };
        }

        private Label ConvertToLabel(LabelData data)
        {
            return new Label
            {
                Id = data.Id,
                Name = data.Name,
                X = data.X,
                Y = data.Y,
                Width = data.Width,
                Height = data.Height,
                Text = data.Text,
                FontSize = data.FontSize,
                TextColor = StringToBrush(data.TextColor),
                FontFamily = data.FontFamily,
                IsBold = data.IsBold,
                IsItalic = data.IsItalic,
                TextAlignment = Enum.Parse<TextAlignment>(data.TextAlignment)
            };
        }

        private string BrushToString(Brush brush)
        {
            if (brush is SolidColorBrush solidBrush)
            {
                return solidBrush.Color.ToString();
            }
            return "#808080"; // 默认灰色
        }

        private Brush StringToBrush(string colorString)
        {
            try
            {
                var converter = new BrushConverter();
                return (Brush)converter.ConvertFromString(colorString)!;
            }
            catch
            {
                return Brushes.Gray; // 默认灰色
            }
        }
    }
}
