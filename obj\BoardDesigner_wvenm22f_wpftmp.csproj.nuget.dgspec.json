{"format": 1, "restore": {"D:\\002_Pensonal\\BoardDev\\BoardDesigner.csproj": {}}, "projects": {"D:\\002_Pensonal\\BoardDev\\BoardDesigner.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\002_Pensonal\\BoardDev\\BoardDesigner.csproj", "projectName": "BoardDesigner", "projectPath": "D:\\002_Pensonal\\BoardDev\\BoardDesigner.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\002_Pensonal\\BoardDev\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"Microsoft.Xaml.Behaviors.Wpf": {"target": "Package", "version": "[1.1.39, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}}}